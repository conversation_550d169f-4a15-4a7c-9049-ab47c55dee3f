-- 设备WiFi配置字段重构迁移脚本
-- {{CHENGQI: Action: Added; Timestamp: 2025-07-28 20:30:00 +08:00; Reason: 重构设备WiFi配置字段为JSON格式; Principle_Applied: 数据结构优化;}}

-- 1. 添加新的wifi_config字段
ALTER TABLE em_device ADD COLUMN wifi_config TEXT COMMENT 'WiFi配置信息(JSON格式)';

-- 2. 数据迁移：将现有WiFi字段合并为JSON格式
-- 注意：此脚本适用于MySQL 8.0+，如果使用较低版本请参考迁移文档中的CONCAT版本

UPDATE em_device 
SET wifi_config = JSON_OBJECT(
  'bitRate', CASE WHEN wifi_bit_rate IS NOT NULL THEN wifi_bit_rate ELSE NULL END,
  'freq', CASE WHEN wifi_freq IS NOT NULL THEN wifi_freq ELSE NULL END,
  'ip', CASE WHEN wifi_ip IS NOT NULL THEN wifi_ip ELSE NULL END,
  'keyMgmt', CASE WHEN wifi_key_mgmt IS NOT NULL THEN wifi_key_mgmt ELSE NULL END,
  'mac', CASE WHEN wifi_mac IS NOT NULL THEN wifi_mac ELSE NULL END,
  'ssid', CASE WHEN wifi_ssid IS NOT NULL THEN wifi_ssid ELSE NULL END,
  'state', CASE WHEN wifi_state IS NOT NULL THEN wifi_state ELSE NULL END,
  'linkQuality', CASE WHEN wifi_link_quality IS NOT NULL THEN wifi_link_quality ELSE NULL END,
  'signalLevel', CASE WHEN wifi_signal_level IS NOT NULL THEN wifi_signal_level ELSE NULL END,
  'gatewayMac', CASE WHEN gateway_mac IS NOT NULL THEN gateway_mac ELSE NULL END
)
WHERE wifi_bit_rate IS NOT NULL 
   OR wifi_freq IS NOT NULL 
   OR wifi_ip IS NOT NULL 
   OR wifi_key_mgmt IS NOT NULL 
   OR wifi_mac IS NOT NULL 
   OR wifi_ssid IS NOT NULL 
   OR wifi_state IS NOT NULL 
   OR wifi_link_quality IS NOT NULL 
   OR wifi_signal_level IS NOT NULL 
   OR gateway_mac IS NOT NULL;

-- 3. 验证迁移结果
SELECT 
  id,
  name,
  wifi_config,
  JSON_VALID(wifi_config) as is_valid_json
FROM em_device 
WHERE wifi_config IS NOT NULL 
LIMIT 5;

-- 4. 显示迁移统计
SELECT 
  COUNT(*) as total_devices,
  COUNT(wifi_config) as devices_with_wifi_config,
  COUNT(CASE WHEN JSON_VALID(wifi_config) = 1 THEN 1 END) as valid_json_configs
FROM em_device;

-- 注意：删除旧字段的操作请在确认数据迁移正确后手动执行
-- 建议先在测试环境验证，然后再在生产环境执行

/*
-- 删除旧字段（请谨慎执行）
-- ALTER TABLE em_device DROP COLUMN wifi_bit_rate;
-- ALTER TABLE em_device DROP COLUMN wifi_freq;
-- ALTER TABLE em_device DROP COLUMN wifi_ip;
-- ALTER TABLE em_device DROP COLUMN wifi_key_mgmt;
-- ALTER TABLE em_device DROP COLUMN wifi_mac;
-- ALTER TABLE em_device DROP COLUMN wifi_ssid;
-- ALTER TABLE em_device DROP COLUMN wifi_state;
-- ALTER TABLE em_device DROP COLUMN wifi_link_quality;
-- ALTER TABLE em_device DROP COLUMN wifi_signal_level;
-- ALTER TABLE em_device DROP COLUMN gateway_mac;
*/
