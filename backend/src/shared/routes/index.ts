/**
 * API路由入口
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-001 项目初始化, 创建路由入口; Principle_Applied: 模块化路由管理;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 14:30:51 +08:00; Reason: 设备管理模块开发, 添加设备路由; Principle_Applied: 模块化路由管理;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 16:45:00 +08:00; Reason: 标签管理模块开发, 添加标签路由; Principle_Applied: 模块化路由管理;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-27 16:30:00 +08:00; Reason: Task-005 工价配置模块开发, 添加工价配置路由; Principle_Applied: 模块化路由管理;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-28 18:30:00 +08:00; Reason: Shrimp Task ID: #64d308e1-c835-4f08-97a8-7f99f430939a, 添加订单管理和设备排产路由; Principle_Applied: 模块化路由管理;}}
 */

import { Router } from 'express';
import { authRoutes } from '../../modules/auth/auth.routes';
import { userRoutes } from '../../modules/user/user.routes';
import { departmentRoutes } from '../../modules/department/department.routes';
import { enterpriseRoutes } from '../../modules/enterprise/enterprise.routes';
import roleRoutes from '../../modules/role/role.routes';
import deviceRoutes from '../../modules/device/device.routes';
import deviceModelRoutes from '../../modules/device/deviceModel.routes';
import tagRoutes from '../../modules/tag/tag.routes';
import patternRoutes from '../../modules/pattern/pattern.routes';
import wageConfigRoutes from '../../modules/wageConfig/wageConfig.routes';
import wageRoutes from '../../modules/wage/wage.routes';
import orderRoutes from '../../modules/order/order.routes';
import deviceOrderSequenceRoutes from '../../modules/order/deviceOrderSequence.routes';
// import deviceCurrentProductionRoutes from '../../modules/production/deviceCurrentProduction.routes';

const router = Router();

// 基础路由
router.get('/', (_req, res) => {
  res.json({
    code: 200,
    message: '刺绣管理系统API服务',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    docs: '/api/v1/docs',
    endpoints: {
      auth: '/api/v1/auth',
      health: '/health',
    },
  });
});

// 认证路由
router.use('/auth', authRoutes);

// 用户管理路由
router.use('/users', userRoutes);

// 部门管理路由
router.use('/departments', departmentRoutes);

// 企业管理路由
router.use('/enterprises', enterpriseRoutes);

// 角色管理路由
router.use('/roles', roleRoutes);

// 设备管理路由
router.use('/devices', deviceRoutes);

// 设备类型管理路由
router.use('/device-models', deviceModelRoutes);

// 标签管理路由
router.use('/tags', tagRoutes);

// 花样管理路由
router.use('/patterns', patternRoutes);

// 工价配置路由
router.use('/wage-configs', wageConfigRoutes);

// 工资记录路由
router.use('/wages', wageRoutes);
router.use('/orders', orderRoutes);
router.use('/sequences', deviceOrderSequenceRoutes);

// 设备当前生产状态路由
// router.use('/device-current-production', deviceCurrentProductionRoutes);

// TODO: 在后续任务中添加其他业务路由
// router.use('/permissions', permissionRoutes);

export { router as apiRoutes };
