/**
 * 创建设备当前生产状态表
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-30 17:45:00 +08:00; Reason: 用户需求，创建设备当前生产状态表的迁移文件; Principle_Applied: 数据库迁移;}}
 */

'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('em_device_current_production', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: '主键ID',
      },
      enterprise_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '企业ID',
        references: {
          model: 'em_enterprises',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      device_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '设备ID',
        references: {
          model: 'em_devices',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      order_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '订单ID',
        references: {
          model: 'em_order',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      sequence_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: '排产记录ID',
        references: {
          model: 'em_device_order_sequence',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      order_code: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: '订单编号（冗余字段，提高查询性能）',
      },
      pattern_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: '花样ID',
        references: {
          model: 'em_patterns',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      start_time: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '开始生产时间',
      },
      estimated_end_time: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '预计完成时间',
      },
      progress: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: '生产进度百分比（0-100）',
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '创建时间',
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: '更新时间',
      },
    });

    // 创建索引
    await queryInterface.addIndex('em_device_current_production', ['enterprise_id'], {
      name: 'device_current_production_enterprise_id_idx',
    });

    await queryInterface.addIndex('em_device_current_production', ['device_id'], {
      name: 'device_current_production_device_id_idx',
    });

    await queryInterface.addIndex('em_device_current_production', ['order_id'], {
      name: 'device_current_production_order_id_idx',
    });

    await queryInterface.addIndex('em_device_current_production', ['sequence_id'], {
      name: 'device_current_production_sequence_id_idx',
      unique: true, // 一个排产记录只能有一条当前生产记录
    });

    await queryInterface.addIndex('em_device_current_production', ['enterprise_id', 'device_id'], {
      name: 'device_current_production_enterprise_device_idx',
    });

    await queryInterface.addIndex('em_device_current_production', ['start_time'], {
      name: 'device_current_production_start_time_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('em_device_current_production');
  },
};
