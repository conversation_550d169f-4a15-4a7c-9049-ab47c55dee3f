/**
 * 数据库迁移：为DeviceOrderSequence表添加生产跟踪字段
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-29 13:30:00 +08:00; Reason: Shrimp Task ID: #b6473b8e-4f99-40d9-a4b1-4b4b52179816, 扩展DeviceOrderSequence数据模型; Principle_Applied: 数据库迁移;}}
 */

import { QueryInterface, DataTypes } from 'sequelize';

export const up = async (queryInterface: QueryInterface): Promise<void> => {
  // 添加生产跟踪字段
  await queryInterface.addColumn('em_device_order_sequence', 'actual_start_time', {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '实际开始生产时间',
  });

  await queryInterface.addColumn('em_device_order_sequence', 'actual_end_time', {
    type: DataTypes.DATE,
    allowNull: true,
    comment: '实际完成生产时间',
  });

  await queryInterface.addColumn('em_device_order_sequence', 'progress', {
    type: DataTypes.INTEGER,
    allowNull: true,
    defaultValue: 0,
    comment: '生产进度百分比 (0-100)',
  });

  await queryInterface.addColumn('em_device_order_sequence', 'actual_quantity', {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: '实际生产数量',
  });

  // 添加索引以优化查询性能
  await queryInterface.addIndex('em_device_order_sequence', ['actual_start_time'], {
    name: 'device_order_sequence_actual_start_time_idx',
  });

  await queryInterface.addIndex('em_device_order_sequence', ['actual_end_time'], {
    name: 'device_order_sequence_actual_end_time_idx',
  });

  await queryInterface.addIndex('em_device_order_sequence', ['progress'], {
    name: 'device_order_sequence_progress_idx',
  });

  // 添加复合索引用于生产统计查询
  await queryInterface.addIndex('em_device_order_sequence', ['enterprise_id', 'actual_start_time'], {
    name: 'device_order_sequence_enterprise_start_time_idx',
  });

  await queryInterface.addIndex('em_device_order_sequence', ['device_id', 'actual_start_time'], {
    name: 'device_order_sequence_device_start_time_idx',
  });
};

export const down = async (queryInterface: QueryInterface): Promise<void> => {
  // 删除索引
  await queryInterface.removeIndex('em_device_order_sequence', 'device_order_sequence_device_start_time_idx');
  await queryInterface.removeIndex('em_device_order_sequence', 'device_order_sequence_enterprise_start_time_idx');
  await queryInterface.removeIndex('em_device_order_sequence', 'device_order_sequence_progress_idx');
  await queryInterface.removeIndex('em_device_order_sequence', 'device_order_sequence_actual_end_time_idx');
  await queryInterface.removeIndex('em_device_order_sequence', 'device_order_sequence_actual_start_time_idx');

  // 删除字段
  await queryInterface.removeColumn('em_device_order_sequence', 'actual_quantity');
  await queryInterface.removeColumn('em_device_order_sequence', 'progress');
  await queryInterface.removeColumn('em_device_order_sequence', 'actual_end_time');
  await queryInterface.removeColumn('em_device_order_sequence', 'actual_start_time');
};
