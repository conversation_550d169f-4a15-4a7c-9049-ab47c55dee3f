/**
 * 数据库连接配置
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 创建数据库连接; Principle_Applied: 数据库连接池管理;}}
 */

import { Sequelize } from 'sequelize';
import { config } from '../../config/app.config';
import { logger } from '../utils/logger';

// 创建Sequelize实例
export const sequelize = new Sequelize({
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  username: config.database.username,
  password: config.database.password,
  dialect: 'postgres',

  // 连接池配置
  pool: {
    max: config.database.pool.max,
    min: config.database.pool.min,
    acquire: config.database.pool.acquire,
    idle: config.database.pool.idle,
    evict: 1000, // 连接回收时间
  },

  // 重试配置
  retry: {
    max: 3, // 最大重试次数
    timeout: 5000, // 重试超时时间
    match: [
      /ETIMEDOUT/,
      /EHOSTUNREACH/,
      /ECONNRESET/,
      /ECONNREFUSED/,
      /ENOTFOUND/,
      /SequelizeConnectionError/,
      /SequelizeConnectionRefusedError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/,
      /SequelizeInvalidConnectionError/,
      /SequelizeConnectionTimedOutError/,
      /remaining connection slots are reserved/
    ]
  },
  
  // 日志配置
  logging: (sql: string) => {
    if (config.app.nodeEnv === 'development') {
      logger.debug('SQL执行', { sql });
    }
  },
  
  // 其他配置
  define: {
    // 自动添加时间戳字段
    timestamps: true,
    // 使用下划线命名
    underscored: true,
    // 禁用复数表名
    freezeTableName: true,
    // 软删除
    paranoid: false,
  },
  
  // 时区配置
  timezone: '+08:00',
});

/**
 * 测试数据库连接
 */
export const testConnection = async (retries: number = 3): Promise<void> => {
  for (let i = 0; i < retries; i++) {
    try {
      await sequelize.authenticate();
      logger.info('数据库连接成功');
      return;
    } catch (error: any) {
      const isLastAttempt = i === retries - 1;

      if (error.message?.includes('remaining connection slots are reserved')) {
        logger.warn(`数据库连接池已满，等待重试 (${i + 1}/${retries})`);
        if (!isLastAttempt) {
          await new Promise(resolve => setTimeout(resolve, 2000 * (i + 1))); // 递增延迟
          continue;
        }
      }

      if (isLastAttempt) {
        logger.error('数据库连接失败', error);
        throw error;
      }

      logger.warn(`数据库连接失败，准备重试 (${i + 1}/${retries})`, error.message);
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
};

/**
 * 同步数据库模型
 */
export const syncDatabase = async (force: boolean = false, retries: number = 3): Promise<void> => {
  for (let i = 0; i < retries; i++) {
    try {
      // 先测试连接
      await testConnection(1);

      // 执行同步
      await sequelize.sync({ force });
      logger.info(`数据库同步完成 ${force ? '(强制重建)' : ''}`);
      return;
    } catch (error: any) {
      const isLastAttempt = i === retries - 1;

      if (error.message?.includes('remaining connection slots are reserved')) {
        logger.warn(`数据库连接池已满，等待重试同步 (${i + 1}/${retries})`);
        if (!isLastAttempt) {
          // 关闭当前连接池，等待一段时间后重试
          await (sequelize.connectionManager as any).pool?.destroyAllNow?.();
          await new Promise(resolve => setTimeout(resolve, 5000 * (i + 1)));
          continue;
        }
      }

      if (error.message?.includes('permission denied for schema public')) {
        logger.error('数据库权限不足，请联系数据库管理员授予CREATE权限');
        throw new Error('数据库权限不足：用户没有在public schema中创建表的权限');
      }

      if (isLastAttempt) {
        logger.error('数据库同步失败', error);
        throw error;
      }

      logger.warn(`数据库同步失败，准备重试 (${i + 1}/${retries})`, error.message);
      await new Promise(resolve => setTimeout(resolve, 2000 * (i + 1)));
    }
  }
};

/**
 * 获取连接池状态
 */
export const getPoolStatus = () => {
  const pool = (sequelize.connectionManager as any).pool;
  if (!pool) {
    return {
      size: 0,
      available: 0,
      using: 0,
      waiting: 0
    };
  }
  return {
    size: pool.size || 0,
    available: pool.available || 0,
    using: pool.using || 0,
    waiting: pool.waiting || 0
  };
};

/**
 * 监控连接池状态
 */
export const monitorPool = () => {
  const status = getPoolStatus();
  logger.info('数据库连接池状态', status);
  return status;
};

/**
 * 关闭数据库连接
 */
export const closeConnection = async (): Promise<void> => {
  try {
    await sequelize.close();
    logger.info('数据库连接已关闭');
  } catch (error) {
    logger.error('关闭数据库连接失败', error);
    throw error;
  }
};

// 导出种子数据函数
export { runAllSeeders } from './seeders';
