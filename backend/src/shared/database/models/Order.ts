/**
 * 订单模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 16:30:00 +08:00; Reason: Shrimp Task ID: #19016318-03b3-4ffb-8c95-3211f0180e92, 创建订单数据模型; Principle_Applied: 数据模型设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 订单状态枚举
export enum OrderStatus {
  UNSCHEDULED = 0,        // 未排产
  PARTIAL_SCHEDULED = 1,  // 部分排产
  SCHEDULED = 2,          // 已排产
  PARTIAL_PRODUCTION = 3, // 部分生产
  IN_PRODUCTION = 4,      // 生产中
  COMPLETED = 5           // 已完成
}

// 订单类型枚举
export enum OrderType {
  DEFAULT = 1,        // 默认
  CUTTING = 2,        // 裁片
  HOME_TEXTILE = 3    // 家纺
}

// 花样信息接口
export interface PatternInfo {
  patternId: string;
  patternQuantity: number;
}

// 订单属性接口
export interface OrderAttributes {
  id: number;
  enterpriseId: number;
  type: OrderType;
  status: OrderStatus;
  code?: string;
  customerName?: string;
  salesman?: string;
  orderDate?: Date;
  deliveryDate?: Date;
  orderUnitId?: number;
  orderQuantity?: number;
  remark?: string;
  orderFiles?: string[];
  patternInfo?: PatternInfo[];
  createdAt: Date;
  updatedAt: Date;
}

// 创建订单时的可选属性
export interface OrderCreationAttributes
  extends Optional<OrderAttributes, 'id' | 'status' | 'code' | 'customerName' | 'salesman' | 'orderDate' | 'deliveryDate' | 'orderUnitId' | 'orderQuantity' | 'remark' | 'orderFiles' | 'patternInfo' | 'createdAt' | 'updatedAt'> {}

// 订单模型类
export class Order extends Model<OrderAttributes, OrderCreationAttributes> implements OrderAttributes {
  public id!: number;
  public enterpriseId!: number;
  public type!: OrderType;
  public status!: OrderStatus;
  public code?: string;
  public customerName?: string;
  public salesman?: string;
  public orderDate?: Date;
  public deliveryDate?: Date;
  public orderUnitId?: number;
  public orderQuantity?: number;
  public remark?: string;
  public orderFiles?: string[];
  public patternInfo?: PatternInfo[];
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public readonly enterprise?: any;
  public readonly deviceOrderSequences?: any[];

  /**
   * 获取状态名称
   */
  public getStatusName(): string {
    const statusNames = {
      [OrderStatus.UNSCHEDULED]: '未排产',
      [OrderStatus.PARTIAL_SCHEDULED]: '部分排产',
      [OrderStatus.SCHEDULED]: '已排产',
      [OrderStatus.PARTIAL_PRODUCTION]: '部分生产',
      [OrderStatus.IN_PRODUCTION]: '生产中',
      [OrderStatus.COMPLETED]: '已完成'
    };
    return statusNames[this.status] || '未知状态';
  }

  /**
   * 获取类型名称
   */
  public getTypeName(): string {
    const typeNames = {
      [OrderType.DEFAULT]: '默认',
      [OrderType.CUTTING]: '裁片',
      [OrderType.HOME_TEXTILE]: '家纺'
    };
    return typeNames[this.type] || '未知类型';
  }

  /**
   * 检查是否可以编辑
   */
  public canEdit(): boolean {
    return this.status === OrderStatus.UNSCHEDULED ||
           this.status === OrderStatus.PARTIAL_SCHEDULED ||
           this.status === OrderStatus.SCHEDULED;
  }

  /**
   * 检查是否可以删除
   */
  public canDelete(): boolean {
    return this.status === OrderStatus.UNSCHEDULED;
  }
}

// 初始化订单模型
Order.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '订单ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    type: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: OrderType.DEFAULT,
      comment: '订单类型',
      validate: {
        isIn: {
          args: [[OrderType.DEFAULT, OrderType.CUTTING, OrderType.HOME_TEXTILE]],
          msg: '订单类型必须是有效值',
        },
      },
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: OrderStatus.UNSCHEDULED,
      comment: '订单状态：未排产、部分排产、已排产、部分生产、生产中、已完成',
      validate: {
        isIn: {
          args: [[OrderStatus.UNSCHEDULED, OrderStatus.PARTIAL_SCHEDULED, OrderStatus.SCHEDULED, OrderStatus.PARTIAL_PRODUCTION, OrderStatus.IN_PRODUCTION, OrderStatus.COMPLETED]],
          msg: '订单状态必须是有效值',
        },
      },
    },
    code: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '订单编号',
      validate: {
        len: {
          args: [1, 100],
          msg: '订单编号长度必须在1-100个字符之间',
        },
      },
    },
    customerName: {
      type: DataTypes.STRING(200),
      allowNull: true,
      field: 'customer_name',
      comment: '客户名称',
      validate: {
        len: {
          args: [1, 200],
          msg: '客户名称长度必须在1-200个字符之间',
        },
      },
    },
    salesman: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: '业务员',
      validate: {
        len: {
          args: [1, 100],
          msg: '业务员名称长度必须在1-100个字符之间',
        },
      },
    },
    orderDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'order_date',
      comment: '下单日期',
    },
    deliveryDate: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'delivery_date',
      comment: '交货日期',
    },
    orderUnitId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'order_unit_id',
      comment: '记量单位id',
    },
    orderQuantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'order_quantity',
      comment: '订单量 = 各花样订单量之和',
      validate: {
        min: {
          args: [0],
          msg: '订单量不能为负数',
        },
      },
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '订单备注',
    },
    orderFiles: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      allowNull: true,
      field: 'order_files',
      comment: '订单文件列表',
      defaultValue: [],
      validate: {
        isValidFileArray(value: string[] | null) {
          if (value === null || value === undefined) return;
          if (!Array.isArray(value)) {
            throw new Error('订单文件必须是数组格式');
          }
          for (const file of value) {
            if (typeof file !== 'string' || file.trim() === '') {
              throw new Error('订单文件路径必须是非空字符串');
            }
          }
        },
      },
    },
    patternInfo: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'pattern_info',
      comment: '花样信息 [{"patternId":"123","patternQuantity":10},{"patternId":"124","patternQuantity":20}]',
      get() {
        const rawValue = this.getDataValue('patternInfo');
        if (!rawValue) return null;
        try {
          return typeof rawValue === 'string' ? JSON.parse(rawValue) : rawValue;
        } catch (error) {
          return null;
        }
      },
      set(value: PatternInfo[] | null) {
        if (value === null || value === undefined) {
          this.setDataValue('patternInfo', null as any);
        } else {
          this.setDataValue('patternInfo', JSON.stringify(value) as any);
        }
      },
      validate: {
        isValidPatternInfo(value: any) {
          if (value === null || value === undefined) return;

          let parsedValue: PatternInfo[];
          try {
            parsedValue = typeof value === 'string' ? JSON.parse(value) : value;
          } catch (error) {
            throw new Error('花样信息必须是有效的JSON格式');
          }

          if (!Array.isArray(parsedValue)) {
            throw new Error('花样信息必须是数组格式');
          }

          for (let i = 0; i < parsedValue.length; i++) {
            const item = parsedValue[i];
            if (!item.patternId || !item.patternQuantity) {
              throw new Error('花样信息项目必须包含patternId和patternQuantity字段');
            }
            if (typeof item.patternId !== 'string' || typeof item.patternQuantity !== 'number') {
              throw new Error('花样信息的patternId必须是字符串，patternQuantity必须是数字');
            }
            if (item.patternQuantity <= 0) {
              throw new Error('花样数量必须大于0');
            }
          }
        },
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_order',
    modelName: 'Order',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'order_enterprise_id_idx',
      },
      {
        fields: ['status'],
        name: 'order_status_idx',
      },
      {
        fields: ['code'],
        name: 'order_code_idx',
      },
      {
        fields: ['customer_name'],
        name: 'order_customer_name_idx',
      },
      {
        fields: ['order_date'],
        name: 'order_order_date_idx',
      },
      {
        fields: ['delivery_date'],
        name: 'order_delivery_date_idx',
      },
      // 复合索引 - 优化多条件查询
      {
        fields: ['enterprise_id', 'status'],
        name: 'order_enterprise_status_idx',
      },
      {
        fields: ['enterprise_id', 'order_date'],
        name: 'order_enterprise_date_idx',
      },
    ],
  }
);
