/**
 * 设备订单排序模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 17:00:00 +08:00; Reason: Shrimp Task ID: #19b76f1a-8b36-4a24-be43-40abd83e0b93, 创建设备订单排序数据模型; Principle_Applied: 数据模型设计;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 13:30:00 +08:00; Reason: Shrimp Task ID: #b6473b8e-4f99-40d9-a4b1-4b4b52179816, 扩展模型添加生产跟踪字段; Principle_Applied: 数据模型扩展;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 设备订单排序状态枚举
export enum DeviceOrderSequenceStatus {
  WAITING = 0,      // 等待中
  IN_PRODUCTION = 1, // 生产中
  COMPLETED = 2     // 已完成
}

// 设备订单排序属性接口
export interface DeviceOrderSequenceAttributes {
  id: number;
  enterpriseId: number;
  deviceId: number;
  orderId: number;
  patternId: number;
  formula?: string;
  lathesNum?: number;
  productionQuantity?: number;
  productionSequence?: number;
  status: DeviceOrderSequenceStatus;
  actualStartTime?: Date;
  actualEndTime?: Date;
  progress?: number;
  actualQuantity?: number;
  createdAt: Date;
  updatedAt: Date;
}

// 创建设备订单排序时的可选属性
export interface DeviceOrderSequenceCreationAttributes
  extends Optional<DeviceOrderSequenceAttributes, 'id' | 'formula' | 'lathesNum' | 'productionQuantity' | 'productionSequence' | 'status' | 'actualStartTime' | 'actualEndTime' | 'progress' | 'actualQuantity' | 'createdAt' | 'updatedAt'> {}

// 设备订单排序模型类
export class DeviceOrderSequence extends Model<DeviceOrderSequenceAttributes, DeviceOrderSequenceCreationAttributes> implements DeviceOrderSequenceAttributes {
  public id!: number;
  public enterpriseId!: number;
  public deviceId!: number;
  public orderId!: number;
  public patternId!: number;
  public formula?: string;
  public lathesNum?: number;
  public productionQuantity?: number;
  public productionSequence?: number;
  public status!: DeviceOrderSequenceStatus;
  public actualStartTime?: Date;
  public actualEndTime?: Date;
  public progress?: number;
  public actualQuantity?: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public readonly enterprise?: any;
  public readonly device?: any;
  public readonly order?: any;
  public readonly pattern?: any;

  /**
   * 获取状态名称
   */
  public getStatusName(): string {
    const statusNames = {
      [DeviceOrderSequenceStatus.WAITING]: '等待中',
      [DeviceOrderSequenceStatus.IN_PRODUCTION]: '生产中',
      [DeviceOrderSequenceStatus.COMPLETED]: '已完成'
    };
    return statusNames[this.status] || '未知状态';
  }

  /**
   * 计算生产数量
   * 生产数量 = 车数 * 设备头数 / 计算公式
   */
  public calculateProductionQuantity(deviceHeadNum: number): number {
    if (!this.lathesNum || !deviceHeadNum) return 0;
    
    // 解析计算公式，支持任意中文单位
    const formula = this.formula || '1件=1头';
    const match = formula.match(/(\d+)[\u4e00-\u9fff]+=(\d+)头/);
    
    if (match) {
      const pieces = parseInt(match[1]);
      const heads = parseInt(match[2]);
      return Math.floor((this.lathesNum * deviceHeadNum * pieces) / heads);
    }
    
    // 默认计算方式：车数 * 设备头数
    return this.lathesNum * deviceHeadNum;
  }

  /**
   * 检查是否可以编辑
   */
  public canEdit(): boolean {
    return this.status === DeviceOrderSequenceStatus.WAITING;
  }

  /**
   * 检查是否可以删除
   */
  public canDelete(): boolean {
    return this.status === DeviceOrderSequenceStatus.WAITING;
  }

  /**
   * 检查是否可以开始生产
   */
  public canStartProduction(): boolean {
    return this.status === DeviceOrderSequenceStatus.WAITING;
  }

  /**
   * 检查是否可以完成生产
   */
  public canCompleteProduction(): boolean {
    return this.status === DeviceOrderSequenceStatus.IN_PRODUCTION;
  }

  /**
   * 检查是否可以开始生产
   */
  public canStart(): boolean {
    return this.status === DeviceOrderSequenceStatus.WAITING;
  }

  /**
   * 检查是否可以完成生产
   */
  public canComplete(): boolean {
    return this.status === DeviceOrderSequenceStatus.IN_PRODUCTION;
  }

  /**
   * 检查是否可以更新进度
   */
  public canUpdateProgress(): boolean {
    return this.status === DeviceOrderSequenceStatus.IN_PRODUCTION;
  }

  /**
   * 获取生产进度百分比
   */
  public getProgressPercentage(): number {
    return this.progress || 0;
  }

  /**
   * 计算生产耗时（分钟）
   */
  public getProductionDuration(): number | null {
    if (!this.actualStartTime) return null;

    const endTime = this.actualEndTime || new Date();
    const startTime = this.actualStartTime;

    return Math.floor((endTime.getTime() - startTime.getTime()) / (1000 * 60));
  }

  /**
   * 检查是否已开始生产
   */
  public isStarted(): boolean {
    return !!this.actualStartTime;
  }

  /**
   * 检查是否已完成生产
   */
  public isCompleted(): boolean {
    return this.status === DeviceOrderSequenceStatus.COMPLETED && !!this.actualEndTime;
  }
}

// 初始化设备订单排序模型
DeviceOrderSequence.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '设备订单排序ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    deviceId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'device_id',
      comment: '设备ID',
      references: {
        model: 'em_device',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    orderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'order_id',
      comment: '订单ID',
      references: {
        model: 'em_order',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    patternId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'pattern_id',
      comment: '花样ID',
      references: {
        model: 'em_pattern',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    formula: {
      type: DataTypes.STRING(50),
      allowNull: true,
      comment: '计算公式 支持任意中文单位=头数',
      defaultValue: '1件=1头',
      validate: {
        isValidFormula(value: string | null) {
          if (value === null || value === undefined) return;
          const pattern = /^\d+[\u4e00-\u9fff]+=\d+头$/;
          if (!pattern.test(value)) {
            throw new Error('计算公式格式必须为"数字单位=数字头"，如"1件=1头"或"1套=1头"');
          }
        },
      },
    },
    lathesNum: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'lathes_num',
      comment: '车数',
      validate: {
        min: {
          args: [0],
          msg: '车数不能为负数',
        },
      },
    },
    productionQuantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'production_quantity',
      comment: '生产数量 = 车数 * 设备头数 / 计算公式',
      validate: {
        min: {
          args: [0],
          msg: '生产数量不能为负数',
        },
      },
    },
    productionSequence: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'production_sequence',
      comment: '生产排序',
      validate: {
        min: {
          args: [1],
          msg: '生产排序必须大于0',
        },
      },
    },
    status: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: DeviceOrderSequenceStatus.WAITING,
      comment: '状态，等待中、生产中、已完成',
      validate: {
        isIn: {
          args: [[DeviceOrderSequenceStatus.WAITING, DeviceOrderSequenceStatus.IN_PRODUCTION, DeviceOrderSequenceStatus.COMPLETED]],
          msg: '状态必须是有效值',
        },
      },
    },
    actualStartTime: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'actual_start_time',
      comment: '实际开始生产时间',
    },
    actualEndTime: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'actual_end_time',
      comment: '实际完成生产时间',
    },
    progress: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: '生产进度百分比 (0-100)',
      validate: {
        min: {
          args: [0],
          msg: '生产进度不能小于0',
        },
        max: {
          args: [100],
          msg: '生产进度不能大于100',
        },
      },
    },
    actualQuantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'actual_quantity',
      comment: '实际生产数量',
      validate: {
        min: {
          args: [0],
          msg: '实际生产数量不能为负数',
        },
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_device_order_sequence',
    modelName: 'DeviceOrderSequence',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'device_order_sequence_enterprise_id_idx',
      },
      {
        fields: ['device_id'],
        name: 'device_order_sequence_device_id_idx',
      },
      {
        fields: ['order_id'],
        name: 'device_order_sequence_order_id_idx',
      },
      {
        fields: ['pattern_id'],
        name: 'device_order_sequence_pattern_id_idx',
      },
      {
        fields: ['status'],
        name: 'device_order_sequence_status_idx',
      },
      {
        fields: ['production_sequence'],
        name: 'device_order_sequence_production_sequence_idx',
      },
      {
        fields: ['actual_start_time'],
        name: 'device_order_sequence_actual_start_time_idx',
      },
      {
        fields: ['actual_end_time'],
        name: 'device_order_sequence_actual_end_time_idx',
      },
      {
        fields: ['progress'],
        name: 'device_order_sequence_progress_idx',
      },
      // 复合索引 - 优化多条件查询
      {
        fields: ['enterprise_id', 'device_id'],
        name: 'device_order_sequence_enterprise_device_idx',
      },
      {
        fields: ['enterprise_id', 'order_id'],
        name: 'device_order_sequence_enterprise_order_idx',
      },
      {
        fields: ['device_id', 'status'],
        name: 'device_order_sequence_device_status_idx',
      },
      {
        fields: ['device_id', 'production_sequence'],
        name: 'device_order_sequence_device_sequence_idx',
      },
      // 唯一索引 - 确保同一设备上的排序不重复
      {
        fields: ['device_id', 'production_sequence'],
        unique: true,
        name: 'device_order_sequence_device_sequence_unique',
        where: {
          production_sequence: {
            [sequelize.Sequelize.Op.ne]: null
          }
        }
      },
    ],
  }
);
