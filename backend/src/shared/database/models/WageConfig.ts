/**
 * 工价配置模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-27 15:50:00 +08:00; Reason: Task-001 创建WageConfig数据模型, 对应em_wage_conf表; Principle_Applied: Sequelize ORM模型设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 激励工价项目接口
export interface IncentiveWageItem {
  cumulative: number; // 累计针数
  reward: number;     // 奖励金额
}

// 工价类型枚举
export enum WageType {
  PATTERN_WAGE = 1,      // 花样工价
  CHANGE_PIECE_WAGE = 2, // 换片单价
  CHANGE_LINE_WAGE = 3   // 换线单价
}

// 工价配置属性接口
export interface WageConfigAttributes {
  id: number;
  enterpriseId: number;                    // 企业ID
  createdUserId?: number;                  // 创建人ID
  updatedUserId?: number;                  // 修改人ID
  patternId?: number;                      // 花样ID (可选，仅花样工价类型需要)
  patternWage?: number;                    // 花样工价 (元/万针)
  incentiveWage?: IncentiveWageItem[];     // 激励工价JSON
  type: WageType;                          // 工价类型
  changePieceWage?: number;                // 换片单价
  changeLineWage?: number;                 // 换线单价
  createdAt: Date;
  updatedAt: Date;
}

// 创建工价配置时的可选属性
export interface WageConfigCreationAttributes
  extends Optional<WageConfigAttributes, 'id' | 'createdUserId' | 'updatedUserId' | 'patternId' | 'patternWage' | 'incentiveWage' | 'changePieceWage' | 'changeLineWage' | 'createdAt' | 'updatedAt'> {}

/**
 * 工价配置模型类
 */
export class WageConfig extends Model<WageConfigAttributes, WageConfigCreationAttributes> implements WageConfigAttributes {
  public id!: number;
  public enterpriseId!: number;
  public createdUserId?: number;
  public updatedUserId?: number;
  public patternId?: number;
  public patternWage?: number;
  public incentiveWage?: IncentiveWageItem[];
  public type!: WageType;
  public changePieceWage?: number;
  public changeLineWage?: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public enterprise?: any;
  public pattern?: any;
  public createdUser?: any;
  public updatedUser?: any;
}

// 初始化工价配置模型
WageConfig.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      comment: '工价配置ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    createdUserId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'created_user_id',
      comment: '创建人ID',
      references: {
        model: 'em_users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    updatedUserId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'updated_user_id',
      comment: '修改人ID',
      references: {
        model: 'em_users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    patternId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'pattern_id',
      comment: '花样ID',
      references: {
        model: 'em_pattern',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    patternWage: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'pattern_wage',
      comment: '花样工价 xxx元/万针',
      validate: {
        min: {
          args: [0],
          msg: '花样工价不能为负数',
        },
      },
    },
    incentiveWage: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'incentive_wage',
      comment: '激励工价，累计xxx针，奖励xxx元 JSON格式',
      get() {
        const rawValue = this.getDataValue('incentiveWage');
        if (!rawValue) return null;
        try {
          return typeof rawValue === 'string' ? JSON.parse(rawValue) : rawValue;
        } catch (error) {
          return null;
        }
      },
      set(value: IncentiveWageItem[] | null) {
        if (value === null || value === undefined) {
          this.setDataValue('incentiveWage', null as any);
        } else {
          this.setDataValue('incentiveWage', JSON.stringify(value) as any);
        }
      },
      validate: {
        isValidIncentiveWage(value: any) {
          if (value === null || value === undefined) return;
          
          let parsedValue: IncentiveWageItem[];
          try {
            parsedValue = typeof value === 'string' ? JSON.parse(value) : value;
          } catch (error) {
            throw new Error('激励工价必须是有效的JSON格式');
          }

          if (!Array.isArray(parsedValue)) {
            throw new Error('激励工价必须是数组格式');
          }

          for (let i = 0; i < parsedValue.length; i++) {
            const item = parsedValue[i];
            if (!item.cumulative || !item.reward) {
              throw new Error('激励工价项目必须包含cumulative和reward字段');
            }
            if (typeof item.cumulative !== 'number' || typeof item.reward !== 'number') {
              throw new Error('激励工价的cumulative和reward必须是数字');
            }
            if (item.cumulative <= 0 || item.reward <= 0) {
              throw new Error('激励工价的cumulative和reward必须大于0');
            }
            // 检查累计针数是否递增
            if (i > 0 && item.cumulative <= parsedValue[i - 1].cumulative) {
              throw new Error('激励工价的累计针数必须递增');
            }
          }
        },
      },
    },
    type: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'type',
      comment: '工价类型：1-花样工价，2-换片单价，3-换线单价',
      validate: {
        isIn: {
          args: [[1, 2, 3]],
          msg: '工价类型必须是1(花样工价)、2(换片单价)或3(换线单价)',
        },
      },
    },
    changePieceWage: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'change_piece_wage',
      comment: '换片单价,与花样无关,全局通用配置',
      validate: {
        min: {
          args: [0],
          msg: '换片单价不能为负数',
        },
      },
    },
    changeLineWage: {
      type: DataTypes.FLOAT,
      allowNull: true,
      field: 'change_line_wage',
      comment: '换线单价,与花样无关,全局通用配置',
      validate: {
        min: {
          args: [0],
          msg: '换线单价不能为负数',
        },
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_wage_conf',
    timestamps: true,
    underscored: true,
    comment: '工价配置表',
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'wage_conf_enterprise_id_idx',
      },
      {
        fields: ['type'],
        name: 'wage_conf_type_idx',
      },
      {
        fields: ['pattern_id'],
        name: 'wage_conf_pattern_id_idx',
      },
      {
        fields: ['created_user_id'],
        name: 'wage_conf_created_user_id_idx',
      },
      {
        fields: ['updated_user_id'],
        name: 'wage_conf_updated_user_id_idx',
      },
      // 复合索引
      {
        fields: ['enterprise_id', 'type'],
        name: 'wage_conf_enterprise_type_idx',
      },
      {
        fields: ['enterprise_id', 'type', 'pattern_id'],
        name: 'wage_conf_enterprise_type_pattern_idx',
      },
    ],
    validate: {
      // 花样工价类型必须有花样ID
      patternWageValidation() {
        if (this.type === WageType.PATTERN_WAGE && !this.patternId) {
          throw new Error('花样工价类型必须指定花样ID');
        }
      },
      // 换片/换线单价类型不能有花样ID
      globalWageValidation() {
        if ((this.type === WageType.CHANGE_PIECE_WAGE || this.type === WageType.CHANGE_LINE_WAGE) && this.patternId) {
          throw new Error('换片单价和换线单价不能指定花样ID');
        }
      },
      // 根据类型验证对应字段
      typeSpecificValidation() {
        if (this.type === WageType.PATTERN_WAGE && (!this.patternWage && !this.incentiveWage)) {
          throw new Error('花样工价类型必须设置花样工价或激励工价');
        }
        if (this.type === WageType.CHANGE_PIECE_WAGE && !this.changePieceWage) {
          throw new Error('换片单价类型必须设置换片单价');
        }
        if (this.type === WageType.CHANGE_LINE_WAGE && !this.changeLineWage) {
          throw new Error('换线单价类型必须设置换线单价');
        }
      },
    },
  }
);
