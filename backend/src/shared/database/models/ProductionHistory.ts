/**
 * 生产历史记录模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-29 13:35:00 +08:00; Reason: Shrimp Task ID: #c655d27e-7c5e-4eb2-9691-4aa21f4ba985, 创建生产历史记录数据模型; Principle_Applied: 数据模型设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 生产历史操作类型枚举
export enum ProductionHistoryActionType {
  START = 'START',           // 开始生产
  PAUSE = 'PAUSE',           // 暂停生产
  RESUME = 'RESUME',         // 恢复生产
  COMPLETE = 'COMPLETE',     // 完成生产
  CANCEL = 'CANCEL',         // 取消生产
  ERROR = 'ERROR',           // 生产异常
  PROGRESS_UPDATE = 'PROGRESS_UPDATE', // 进度更新
  QUANTITY_UPDATE = 'QUANTITY_UPDATE'  // 数量更新
}

// 生产历史记录属性接口
export interface ProductionHistoryAttributes {
  id: number;
  enterpriseId: number;
  sequenceId: number;
  actionType: ProductionHistoryActionType;
  actionTime: Date;
  operatorId?: number;
  previousValue?: string;
  newValue?: string;
  remark?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 创建生产历史记录时的可选属性
export interface ProductionHistoryCreationAttributes
  extends Optional<ProductionHistoryAttributes, 'id' | 'operatorId' | 'previousValue' | 'newValue' | 'remark' | 'createdAt' | 'updatedAt'> {}

// 生产历史记录模型类
export class ProductionHistory extends Model<ProductionHistoryAttributes, ProductionHistoryCreationAttributes> implements ProductionHistoryAttributes {
  public id!: number;
  public enterpriseId!: number;
  public sequenceId!: number;
  public actionType!: ProductionHistoryActionType;
  public actionTime!: Date;
  public operatorId?: number;
  public previousValue?: string;
  public newValue?: string;
  public remark?: string;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public readonly enterprise?: any;
  public readonly sequence?: any;
  public readonly operator?: any;

  /**
   * 获取操作类型名称
   */
  public getActionTypeName(): string {
    const actionTypeNames = {
      [ProductionHistoryActionType.START]: '开始生产',
      [ProductionHistoryActionType.PAUSE]: '暂停生产',
      [ProductionHistoryActionType.RESUME]: '恢复生产',
      [ProductionHistoryActionType.COMPLETE]: '完成生产',
      [ProductionHistoryActionType.CANCEL]: '取消生产',
      [ProductionHistoryActionType.ERROR]: '生产异常',
      [ProductionHistoryActionType.PROGRESS_UPDATE]: '进度更新',
      [ProductionHistoryActionType.QUANTITY_UPDATE]: '数量更新'
    };
    return actionTypeNames[this.actionType] || '未知操作';
  }

  /**
   * 检查是否为状态变更操作
   */
  public isStatusChange(): boolean {
    return [
      ProductionHistoryActionType.START,
      ProductionHistoryActionType.PAUSE,
      ProductionHistoryActionType.RESUME,
      ProductionHistoryActionType.COMPLETE,
      ProductionHistoryActionType.CANCEL
    ].includes(this.actionType);
  }

  /**
   * 检查是否为数据更新操作
   */
  public isDataUpdate(): boolean {
    return [
      ProductionHistoryActionType.PROGRESS_UPDATE,
      ProductionHistoryActionType.QUANTITY_UPDATE
    ].includes(this.actionType);
  }

  /**
   * 获取变更描述
   */
  public getChangeDescription(): string {
    if (this.previousValue && this.newValue) {
      return `从 ${this.previousValue} 变更为 ${this.newValue}`;
    }
    return this.remark || this.getActionTypeName();
  }
}

// 初始化生产历史记录模型
ProductionHistory.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '生产历史记录ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    sequenceId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'sequence_id',
      comment: '排产记录ID',
      references: {
        model: 'em_device_order_sequence',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    actionType: {
      type: DataTypes.STRING(50),
      allowNull: false,
      field: 'action_type',
      comment: '操作类型',
      validate: {
        isIn: {
          args: [[
            ProductionHistoryActionType.START,
            ProductionHistoryActionType.PAUSE,
            ProductionHistoryActionType.RESUME,
            ProductionHistoryActionType.COMPLETE,
            ProductionHistoryActionType.CANCEL,
            ProductionHistoryActionType.ERROR,
            ProductionHistoryActionType.PROGRESS_UPDATE,
            ProductionHistoryActionType.QUANTITY_UPDATE
          ]],
          msg: '操作类型必须是有效值',
        },
      },
    },
    actionTime: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'action_time',
      comment: '操作时间',
    },
    operatorId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'operator_id',
      comment: '操作员ID',
      references: {
        model: 'em_users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    previousValue: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'previous_value',
      comment: '变更前的值',
    },
    newValue: {
      type: DataTypes.TEXT,
      allowNull: true,
      field: 'new_value',
      comment: '变更后的值',
    },
    remark: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: '备注说明',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_production_history',
    modelName: 'ProductionHistory',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'production_history_enterprise_id_idx',
      },
      {
        fields: ['sequence_id'],
        name: 'production_history_sequence_id_idx',
      },
      {
        fields: ['action_type'],
        name: 'production_history_action_type_idx',
      },
      {
        fields: ['action_time'],
        name: 'production_history_action_time_idx',
      },
      {
        fields: ['operator_id'],
        name: 'production_history_operator_id_idx',
      },
      // 复合索引 - 优化多条件查询
      {
        fields: ['enterprise_id', 'sequence_id'],
        name: 'production_history_enterprise_sequence_idx',
      },
      {
        fields: ['enterprise_id', 'action_time'],
        name: 'production_history_enterprise_time_idx',
      },
      {
        fields: ['sequence_id', 'action_time'],
        name: 'production_history_sequence_time_idx',
      },
      {
        fields: ['enterprise_id', 'action_type'],
        name: 'production_history_enterprise_action_idx',
      },
    ],
  }
);

export default ProductionHistory;
