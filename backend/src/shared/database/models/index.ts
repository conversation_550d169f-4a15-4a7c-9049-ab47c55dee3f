/**
 * 数据库模型入口和关联配置
 * {{CHENGQI: Action: Added; Timestamp: 2025-06-27 09:53:22 +08:00; Reason: Task-002 数据库初始化, 配置模型关联关系; Principle_Applied: 数据库关联设计;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 14:30:51 +08:00; Reason: 设备管理模块开发, 添加设备模型关联; Principle_Applied: 数据库关联设计;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-23 16:45:00 +08:00; Reason: 标签管理模块开发, 添加标签模型关联; Principle_Applied: 数据库关联设计;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-27 15:55:00 +08:00; Reason: Task-002 工价配置模块开发, 添加WageConfig模型关联; Principle_Applied: 数据库关联设计;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 13:35:00 +08:00; Reason: Shrimp Task ID: #c655d27e-7c5e-4eb2-9691-4aa21f4ba985, 添加ProductionHistory模型关联; Principle_Applied: 数据库关联设计;}}
 */

import { sequelize } from '../index';

// 导入所有模型
import { Enterprise } from './Enterprise';
import { Department } from './Department';
import { User } from './User';
import { Role } from './Role';
import { Permission } from './Permission';
import { UserRole } from './UserRole';
import { RolePermission } from './RolePermission';
import { Tag } from './Tag';
import { DeviceModel } from './DeviceModel';
import { Device } from './Device';
import { Pattern } from './Pattern';
import { WageConfig } from './WageConfig';
import { Wage } from './Wage';
import { Order } from './Order';
import { DeviceOrderSequence } from './DeviceOrderSequence';
import { DeviceCurrentProduction } from './DeviceCurrentProduction';
import { ProductionHistory } from './ProductionHistory';

// 配置模型关联关系
export const initializeAssociations = (): void => {
  // 企业与部门的关联 (一对多)
  Enterprise.hasMany(Department, {
    foreignKey: 'enterpriseId',
    as: 'departments',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  Department.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 部门的自关联 (树形结构)
  Department.hasMany(Department, {
    foreignKey: 'parentId',
    as: 'children',
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  Department.belongsTo(Department, {
    foreignKey: 'parentId',
    as: 'parent',
  });

  // 企业与用户的关联 (一对多)
  Enterprise.hasMany(User, {
    foreignKey: 'enterpriseId',
    as: 'users',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  User.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 部门与用户的关联 (一对多)
  Department.hasMany(User, {
    foreignKey: 'departmentId',
    as: 'users',
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  User.belongsTo(Department, {
    foreignKey: 'departmentId',
    as: 'department',
  });

  // 企业与角色的关联 (一对多)
  Enterprise.hasMany(Role, {
    foreignKey: 'enterpriseId',
    as: 'roles',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  Role.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 用户与角色的多对多关联
  User.belongsToMany(Role, {
    through: UserRole,
    foreignKey: 'userId',
    otherKey: 'roleId',
    as: 'roles',
  });
  Role.belongsToMany(User, {
    through: UserRole,
    foreignKey: 'roleId',
    otherKey: 'userId',
    as: 'users',
  });

  // 角色与权限的多对多关联
  Role.belongsToMany(Permission, {
    through: RolePermission,
    foreignKey: 'roleId',
    otherKey: 'permissionId',
    as: 'permissions',
  });
  Permission.belongsToMany(Role, {
    through: RolePermission,
    foreignKey: 'permissionId',
    otherKey: 'roleId',
    as: 'roles',
  });

  // 用户角色关联表的关联
  UserRole.belongsTo(User, {
    foreignKey: 'userId',
    as: 'user',
  });
  UserRole.belongsTo(Role, {
    foreignKey: 'roleId',
    as: 'role',
  });

  // 角色权限关联表的关联
  RolePermission.belongsTo(Role, {
    foreignKey: 'roleId',
    as: 'role',
  });
  RolePermission.belongsTo(Permission, {
    foreignKey: 'permissionId',
    as: 'permission',
  });

  // 企业与设备的关联 (一对多)
  Enterprise.hasMany(Device, {
    foreignKey: 'enterpriseId',
    as: 'devices',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  Device.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 企业与设备类型的关联 (一对多)
  Enterprise.hasMany(DeviceModel, {
    foreignKey: 'enterpriseId',
    as: 'deviceModels',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceModel.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 设备类型与设备的关联 (一对多) - 设备可以引用设备类型
  DeviceModel.hasMany(Device, {
    foreignKey: 'deviceModelId',
    as: 'devices',
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  Device.belongsTo(DeviceModel, {
    foreignKey: 'deviceModelId',
    as: 'deviceModelInfo',
  });

  // 标签与设备的关联 (一对多) - 设备可以引用车间产线标签
  Tag.hasMany(Device, {
    foreignKey: 'productionLineId',
    as: 'productionLineDevices',
    constraints: false, // 因为Tag表有type字段区分不同类型
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  Device.belongsTo(Tag, {
    foreignKey: 'productionLineId',
    as: 'productionLine',
    constraints: false,
  });

  // 标签与设备的关联 (一对多) - 设备可以引用机器分组标签
  Tag.hasMany(Device, {
    foreignKey: 'groupId',
    as: 'groupDevices',
    constraints: false, // 因为Tag表有type字段区分不同类型
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  Device.belongsTo(Tag, {
    foreignKey: 'groupId',
    as: 'group',
    constraints: false,
  });

  // 企业与花样的关联 (一对多)
  Enterprise.hasMany(Pattern, {
    foreignKey: 'enterpriseId',
    as: 'patterns',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  Pattern.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 标签与花样的关联 (一对多) - 花样可以引用花样分组标签
  Tag.hasMany(Pattern, {
    foreignKey: 'groupId',
    as: 'patterns',
    constraints: false, // 因为Tag表有type字段区分不同类型
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  Pattern.belongsTo(Tag, {
    foreignKey: 'groupId',
    as: 'group',
    constraints: false,
  });

  // 企业与标签的关联 (一对多)
  Enterprise.hasMany(Tag, {
    foreignKey: 'enterpriseId',
    as: 'tags',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  Tag.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 标签的自关联 (树形结构)
  Tag.hasMany(Tag, {
    foreignKey: 'pid',
    as: 'children',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  Tag.belongsTo(Tag, {
    foreignKey: 'pid',
    as: 'parent',
  });

  // 企业与工价配置的关联 (一对多)
  Enterprise.hasMany(WageConfig, {
    foreignKey: 'enterpriseId',
    as: 'wageConfigs',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  WageConfig.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 花样与工价配置的关联 (一对多) - 可选关联，仅花样工价类型需要
  Pattern.hasMany(WageConfig, {
    foreignKey: 'patternId',
    as: 'wageConfigs',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  WageConfig.belongsTo(Pattern, {
    foreignKey: 'patternId',
    as: 'pattern',
  });

  // 用户与工价配置的关联 (一对多) - 创建人
  User.hasMany(WageConfig, {
    foreignKey: 'createdUserId',
    as: 'createdWageConfigs',
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  WageConfig.belongsTo(User, {
    foreignKey: 'createdUserId',
    as: 'createdUser',
  });

  // 用户与工价配置的关联 (一对多) - 修改人
  User.hasMany(WageConfig, {
    foreignKey: 'updatedUserId',
    as: 'updatedWageConfigs',
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  WageConfig.belongsTo(User, {
    foreignKey: 'updatedUserId',
    as: 'updatedUser',
  });

  // 企业与工资记录的关联 (一对多)
  Enterprise.hasMany(Wage, {
    foreignKey: 'enterpriseId',
    as: 'wages',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  Wage.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 企业与订单的关联 (一对多)
  Enterprise.hasMany(Order, {
    foreignKey: 'enterpriseId',
    as: 'orders',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  Order.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 订单与设备排产的关联 (一对多)
  Order.hasMany(DeviceOrderSequence, {
    foreignKey: 'orderId',
    as: 'deviceOrderSequences',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceOrderSequence.belongsTo(Order, {
    foreignKey: 'orderId',
    as: 'order',
  });

  // 设备与设备排产的关联 (一对多)
  Device.hasMany(DeviceOrderSequence, {
    foreignKey: 'deviceId',
    as: 'deviceOrderSequences',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceOrderSequence.belongsTo(Device, {
    foreignKey: 'deviceId',
    as: 'device',
  });

  // 花样与设备排产的关联 (一对多)
  Pattern.hasMany(DeviceOrderSequence, {
    foreignKey: 'patternId',
    as: 'deviceOrderSequences',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceOrderSequence.belongsTo(Pattern, {
    foreignKey: 'patternId',
    as: 'pattern',
  });

  // 企业与设备排产的关联 (一对多)
  Enterprise.hasMany(DeviceOrderSequence, {
    foreignKey: 'enterpriseId',
    as: 'deviceOrderSequences',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceOrderSequence.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 设备排产与生产历史的关联 (一对多)
  DeviceOrderSequence.hasMany(ProductionHistory, {
    foreignKey: 'sequenceId',
    as: 'productionHistories',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  ProductionHistory.belongsTo(DeviceOrderSequence, {
    foreignKey: 'sequenceId',
    as: 'sequence',
  });

  // 企业与生产历史的关联 (一对多)
  Enterprise.hasMany(ProductionHistory, {
    foreignKey: 'enterpriseId',
    as: 'productionHistories',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  ProductionHistory.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 用户与生产历史的关联 (一对多) - 操作员关联
  User.hasMany(ProductionHistory, {
    foreignKey: 'operatorId',
    as: 'productionHistories',
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  ProductionHistory.belongsTo(User, {
    foreignKey: 'operatorId',
    as: 'operator',
  });

  // 设备当前生产状态的关联关系
  // 企业与设备当前生产状态的关联 (一对多)
  Enterprise.hasMany(DeviceCurrentProduction, {
    foreignKey: 'enterpriseId',
    as: 'deviceCurrentProductions',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceCurrentProduction.belongsTo(Enterprise, {
    foreignKey: 'enterpriseId',
    as: 'enterprise',
  });

  // 设备与设备当前生产状态的关联 (一对一，每台设备只能有一条当前生产记录)
  Device.hasOne(DeviceCurrentProduction, {
    foreignKey: 'deviceId',
    as: 'currentProduction',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceCurrentProduction.belongsTo(Device, {
    foreignKey: 'deviceId',
    as: 'device',
  });

  // 订单与设备当前生产状态的关联 (一对一，每个订单只能有一条当前生产记录)
  Order.hasOne(DeviceCurrentProduction, {
    foreignKey: 'orderId',
    as: 'currentProduction',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceCurrentProduction.belongsTo(Order, {
    foreignKey: 'orderId',
    as: 'order',
  });

  // 设备排产与设备当前生产状态的关联 (一对一)
  DeviceOrderSequence.hasOne(DeviceCurrentProduction, {
    foreignKey: 'sequenceId',
    as: 'currentProduction',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceCurrentProduction.belongsTo(DeviceOrderSequence, {
    foreignKey: 'sequenceId',
    as: 'sequence',
  });

  // 花样与设备当前生产状态的关联 (一对一，每个花样只能有一条当前生产记录)
  Pattern.hasOne(DeviceCurrentProduction, {
    foreignKey: 'patternId',
    as: 'currentProduction',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  });
  DeviceCurrentProduction.belongsTo(Pattern, {
    foreignKey: 'patternId',
    as: 'pattern',
  });

  // 设备与工资记录的关联 (一对多) - 可选关联
  Device.hasMany(Wage, {
    foreignKey: 'deviceId',
    as: 'wages',
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  Wage.belongsTo(Device, {
    foreignKey: 'deviceId',
    as: 'device',
  });

  // 花样与工资记录的关联 (一对多) - 可选关联
  Pattern.hasMany(Wage, {
    foreignKey: 'patternId',
    as: 'wages',
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE',
  });
  Wage.belongsTo(Pattern, {
    foreignKey: 'patternId',
    as: 'pattern',
  });

  // 用户与工资记录的关联 (一对多) - 通过userCode关联，不是外键约束
  // 这里不设置直接关联，因为userCode是编码而不是ID
};

// 导出所有模型
export {
  sequelize,
  Enterprise,
  Department,
  User,
  Role,
  Permission,
  UserRole,
  RolePermission,
  Tag,
  DeviceModel,
  Device,
  Pattern,
  WageConfig,
  Wage,
  Order,
  DeviceOrderSequence,
  DeviceCurrentProduction,
  ProductionHistory,
};

// 导出模型类型
export type {
  EnterpriseAttributes,
  EnterpriseCreationAttributes,
} from './Enterprise';

export type {
  DepartmentAttributes,
  DepartmentCreationAttributes,
} from './Department';

export type {
  UserAttributes,
  UserCreationAttributes,
} from './User';

export type {
  RoleAttributes,
  RoleCreationAttributes,
} from './Role';

export type {
  PermissionAttributes,
  PermissionCreationAttributes,
} from './Permission';

export type {
  UserRoleAttributes,
  UserRoleCreationAttributes,
} from './UserRole';

export type {
  RolePermissionAttributes,
  RolePermissionCreationAttributes,
} from './RolePermission';

export type {
  TagAttributes,
  TagCreationAttributes,
} from './Tag';

export type {
  DeviceModelAttributes,
  DeviceModelCreationAttributes,
} from './DeviceModel';

export type {
  DeviceAttributes,
  DeviceCreationAttributes,
} from './Device';

export type {
  PatternAttributes,
  PatternCreationAttributes,
} from './Pattern';

export type {
  WageConfigAttributes,
  WageConfigCreationAttributes,
  IncentiveWageItem,
  WageType,
} from './WageConfig';

export type {
  WageAttributes,
  WageCreationAttributes,
} from './Wage';

export type {
  OrderAttributes,
  OrderCreationAttributes,
  OrderStatus,
  OrderType,
  PatternInfo,
} from './Order';

export type {
  DeviceOrderSequenceAttributes,
  DeviceOrderSequenceCreationAttributes,
  DeviceOrderSequenceStatus,
} from './DeviceOrderSequence';

export type {
  DeviceCurrentProductionAttributes,
  DeviceCurrentProductionCreationAttributes,
} from './DeviceCurrentProduction';

export type {
  ProductionHistoryAttributes,
  ProductionHistoryCreationAttributes,
  ProductionHistoryActionType,
} from './ProductionHistory';
