/**
 * 设备当前生产状态模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-30 17:45:00 +08:00; Reason: 用户需求，创建设备当前生产状态表; Principle_Applied: 数据模型设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 设备当前生产状态属性接口
export interface DeviceCurrentProductionAttributes {
  id: number;
  enterpriseId: number;
  deviceId: number;
  deviceSn: string;
  orderId: number;
  orderCode: string;
  patternId: number;
  patternCode: string;
  sequenceId: number;
  createdAt: Date;
  updatedAt: Date;
}

// 创建设备当前生产状态时的可选属性
export interface DeviceCurrentProductionCreationAttributes
  extends Optional<DeviceCurrentProductionAttributes, 'id' | 'createdAt' | 'updatedAt'> {}

// 设备当前生产状态模型类
export class DeviceCurrentProduction extends Model<DeviceCurrentProductionAttributes, DeviceCurrentProductionCreationAttributes> implements DeviceCurrentProductionAttributes {
  public id!: number;
  public enterpriseId!: number;
  public deviceId!: number;
  public deviceSn!: string;
  public orderId!: number;
  public orderCode!: string;
  public patternId!: number;
  public patternCode!: string;
  public sequenceId!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public readonly device?: any;
  public readonly order?: any;
  public readonly sequence?: any;
  public readonly pattern?: any;
  public readonly enterprise?: any;

  /**
   * 根据设备SN查找当前生产记录
   */
  public static async findByDeviceSn(deviceSn: string, enterpriseId: number): Promise<DeviceCurrentProduction | null> {
    return await DeviceCurrentProduction.findOne({
      where: {
        deviceSn,
        enterpriseId
      },
      include: [
        {
          model: require('./Device').Device,
          as: 'device',
          attributes: ['id', 'name', 'code', 'status']
        },
        {
          model: require('./Order').Order,
          as: 'order',
          attributes: ['id', 'customerName', 'orderQuantity']
        },
        {
          model: require('./Pattern').Pattern,
          as: 'pattern',
          attributes: ['id', 'name']
        }
      ]
    });
  }
}

// 初始化设备当前生产状态模型
DeviceCurrentProduction.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '主键ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    deviceId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'device_id',
      comment: '设备ID',
      references: {
        model: 'em_device',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    deviceSn: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'device_sn',
      comment: '设备SN，唯一标识',
      unique: true,
    },
    orderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'order_id',
      comment: '订单ID',
      references: {
        model: 'em_order',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    orderCode: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'order_code',
      comment: '订单编号，唯一标识',
      unique: true,
    },
    patternId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'pattern_id',
      comment: '花样ID',
      references: {
        model: 'em_pattern',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    patternCode: {
      type: DataTypes.STRING(100),
      allowNull: false,
      field: 'pattern_code',
      comment: '花样编码，唯一标识',
      unique: true,
    },
  sequenceId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'sequence_id',
      comment: '排产记录ID',
      references: {
        model: 'em_device_order_sequence',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_device_current_production',
    modelName: 'DeviceCurrentProduction',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'device_current_production_enterprise_id_idx',
      },
      {
        fields: ['device_sn'],
        name: 'device_current_production_device_sn_idx',
        unique: true, // 每个设备SN只能有一条当前生产记录
      },
      {
        fields: ['order_code'],
        name: 'device_current_production_order_code_idx',
        unique: true, // 每个订单编号只能有一条当前生产记录
      },
      {
        fields: ['pattern_code'],
        name: 'device_current_production_pattern_code_idx',
        unique: true, // 每个花样编码只能有一条当前生产记录
      },
      {
        fields: ['sequence_id'],
        name: 'device_current_production_sequence_id_idx',
        unique: true, // 一个排产记录只能有一条当前生产记录
      },
      {
        fields: ['enterprise_id', 'device_id'],
        name: 'device_current_production_enterprise_device_idx',
        unique: true, // 每个企业的每台设备只能有一条当前生产记录
      },
    ],
  }
);
