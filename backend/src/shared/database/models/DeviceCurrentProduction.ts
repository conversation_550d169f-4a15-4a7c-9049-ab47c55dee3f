/**
 * 设备当前生产状态模型
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-30 17:45:00 +08:00; Reason: 用户需求，创建设备当前生产状态表; Principle_Applied: 数据模型设计;}}
 */

import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../index';

// 设备当前生产状态属性接口
export interface DeviceCurrentProductionAttributes {
  id: number;
  enterpriseId: number;
  deviceId: number;
  orderId: number;
  sequenceId: number;
  orderCode?: string;
  patternId?: number;
  startTime: Date;
  estimatedEndTime?: Date;
  progress: number;
  createdAt: Date;
  updatedAt: Date;
}

// 创建设备当前生产状态时的可选属性
export interface DeviceCurrentProductionCreationAttributes
  extends Optional<DeviceCurrentProductionAttributes, 'id' | 'orderCode' | 'patternId' | 'estimatedEndTime' | 'progress' | 'createdAt' | 'updatedAt'> {}

// 设备当前生产状态模型类
export class DeviceCurrentProduction extends Model<DeviceCurrentProductionAttributes, DeviceCurrentProductionCreationAttributes> implements DeviceCurrentProductionAttributes {
  public id!: number;
  public enterpriseId!: number;
  public deviceId!: number;
  public orderId!: number;
  public sequenceId!: number;
  public orderCode?: string;
  public patternId?: number;
  public startTime!: Date;
  public estimatedEndTime?: Date;
  public progress!: number;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // 关联属性
  public readonly device?: any;
  public readonly order?: any;
  public readonly sequence?: any;
  public readonly pattern?: any;
  public readonly enterprise?: any;

  /**
   * 更新生产进度
   */
  public async updateProgress(progress: number): Promise<void> {
    if (progress < 0 || progress > 100) {
      throw new Error('生产进度必须在0-100之间');
    }
    await this.update({ progress });
  }

  /**
   * 检查是否已超时
   */
  public isOverdue(): boolean {
    if (!this.estimatedEndTime) return false;
    return new Date() > this.estimatedEndTime;
  }

  /**
   * 获取生产持续时间（分钟）
   */
  public getProductionDuration(): number {
    const now = new Date();
    const diffMs = now.getTime() - this.startTime.getTime();
    return Math.floor(diffMs / (1000 * 60));
  }
}

// 初始化设备当前生产状态模型
DeviceCurrentProduction.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: '主键ID',
    },
    enterpriseId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'enterprise_id',
      comment: '企业ID',
      references: {
        model: 'em_enterprises',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    deviceId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'device_id',
      comment: '设备ID',
      references: {
        model: 'em_devices',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    orderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'order_id',
      comment: '订单ID',
      references: {
        model: 'em_order',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    sequenceId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      field: 'sequence_id',
      comment: '排产记录ID',
      references: {
        model: 'em_device_order_sequence',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    orderCode: {
      type: DataTypes.STRING(100),
      allowNull: true,
      field: 'order_code',
      comment: '订单编号（冗余字段，提高查询性能）',
    },
    patternId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      field: 'pattern_id',
      comment: '花样ID',
      references: {
        model: 'em_patterns',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    startTime: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'start_time',
      comment: '开始生产时间',
    },
    estimatedEndTime: {
      type: DataTypes.DATE,
      allowNull: true,
      field: 'estimated_end_time',
      comment: '预计完成时间',
    },
    progress: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: '生产进度百分比（0-100）',
      validate: {
        min: {
          args: [0],
          msg: '生产进度不能小于0',
        },
        max: {
          args: [100],
          msg: '生产进度不能大于100',
        },
      },
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'created_at',
      comment: '创建时间',
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
      field: 'updated_at',
      comment: '更新时间',
    },
  },
  {
    sequelize,
    tableName: 'em_device_current_production',
    modelName: 'DeviceCurrentProduction',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['enterprise_id'],
        name: 'device_current_production_enterprise_id_idx',
      },
      {
        fields: ['device_id'],
        name: 'device_current_production_device_id_idx',
      },
      {
        fields: ['order_id'],
        name: 'device_current_production_order_id_idx',
      },
      {
        fields: ['sequence_id'],
        name: 'device_current_production_sequence_id_idx',
        unique: true, // 一个排产记录只能有一条当前生产记录
      },
      {
        fields: ['enterprise_id', 'device_id'],
        name: 'device_current_production_enterprise_device_idx',
      },
      {
        fields: ['start_time'],
        name: 'device_current_production_start_time_idx',
      },
    ],
  }
);
