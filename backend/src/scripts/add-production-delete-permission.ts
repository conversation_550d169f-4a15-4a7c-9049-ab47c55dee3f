/**
 * 添加缺失的 production:delete 权限并分配给相应角色
 */

import { sequelize } from '@/shared/database';
import { Role } from '@/shared/database/models/Role';
import { Permission } from '@/shared/database/models/Permission';
import { Enterprise } from '@/shared/database/models/Enterprise';
import { logger } from '@/shared/utils/logger';
import { Op } from 'sequelize';

async function addProductionDeletePermission() {
  const transaction = await sequelize.transaction();
  
  try {
    logger.info('开始添加 production:delete 权限...');

    // 1. 检查权限是否已存在
    const existingPermission = await Permission.findOne({
      where: { code: 'production:delete' },
      transaction
    });

    let permission;
    if (existingPermission) {
      logger.info('production:delete 权限已存在');
      permission = existingPermission;
    } else {
      // 2. 创建 production:delete 权限
      permission = await Permission.create({
        name: '删除生产计划',
        code: 'production:delete',
        module: 'production',
        description: '删除生产计划'
      }, { transaction });
      logger.info('创建 production:delete 权限成功');
    }

    // 3. 查找所有需要这个权限的角色
    // 企业管理员、老板角色应该拥有生产计划的所有权限
    const rolesToUpdate = await Role.findAll({
      where: {
        code: {
          [Op.in]: ['ENTERPRISE_ADMIN', 'BOSS']
        }
      },
      include: [
        {
          model: Permission,
          as: 'permissions',
          where: {
            code: {
              [Op.in]: ['production:view', 'production:create', 'production:update']
            }
          },
          required: true
        }
      ],
      transaction
    });

    logger.info(`找到 ${rolesToUpdate.length} 个需要更新的角色`);

    // 4. 为这些角色添加 production:delete 权限
    for (const role of rolesToUpdate) {
      // 获取角色当前权限
      const currentPermissions = await role.getPermissions();
      const hasPermission = currentPermissions.some(p => p.code === 'production:delete');

      if (!hasPermission) {
        await role.setPermissions([...currentPermissions, permission]);
        logger.info(`为角色 ${role.name} (${role.code}) 添加 production:delete 权限`);
      } else {
        logger.info(`角色 ${role.name} (${role.code}) 已有 production:delete 权限`);
      }
    }

    // 5. 特别处理：为车间主任角色添加生产计划权限（如果存在）
    const workshopManagerRoles = await Role.findAll({
      where: {
        code: 'WORKSHOP_MANAGER'
      },
      transaction
    });

    for (const role of workshopManagerRoles) {
      const currentPermissions = await role.getPermissions();
      const hasPermission = currentPermissions.some(p => p.code === 'production:delete');

      if (!hasPermission) {
        await role.setPermissions([...currentPermissions, permission]);
        logger.info(`为车间主任角色 ${role.name} 添加 production:delete 权限`);
      }
    }

    await transaction.commit();
    logger.info('production:delete 权限添加完成');

  } catch (error) {
    await transaction.rollback();
    logger.error('添加 production:delete 权限失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addProductionDeletePermission()
    .then(() => {
      logger.info('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('脚本执行失败:', error);
      process.exit(1);
    });
}

export { addProductionDeletePermission };
