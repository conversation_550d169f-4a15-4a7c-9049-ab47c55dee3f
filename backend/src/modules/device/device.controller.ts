/**
 * 设备控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-23 14:30:51 +08:00; Reason: 设备管理模块开发, 创建设备HTTP接口控制器; Principle_Applied: 控制器模式;}}
 */

import { Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { DeviceService, DeviceListQuery, CreateDeviceRequest, UpdateDeviceRequest, UpdateWifiConfigRequest } from './device.service';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

const deviceService = new DeviceService();

export class DeviceController {
  /**
   * 获取设备列表
   */
  async getDeviceList(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const query: DeviceListQuery = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 10,
        search: req.query.search as string,
        deviceModelId: req.query.deviceModelId ? parseInt(req.query.deviceModelId as string) : undefined,
        productionLineId: req.query.productionLineId ? parseInt(req.query.productionLineId as string) : undefined,
        groupId: req.query.groupId ? parseInt(req.query.groupId as string) : undefined,
        vendor: req.query.vendor as string,
        enterpriseId: req.user!.enterpriseId
      };

      const result = await deviceService.getDeviceList(query);

      res.json({
        code: 200,
        message: '获取设备列表成功',
        data: result
      });
    } catch (error) {
      logger.error('获取设备列表失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取设备详情
   */
  async getDeviceById(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const deviceId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      const device = await deviceService.getDeviceById(deviceId, enterpriseId);

      res.json({
        code: 200,
        message: '获取设备详情成功',
        data: device
      });
    } catch (error) {
      logger.error('获取设备详情失败', {
        deviceId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建设备
   */
  async createDevice(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const data: CreateDeviceRequest = {
        ...req.body,
        enterpriseId: req.user!.enterpriseId
      };

      const device = await deviceService.createDevice(data);

      res.status(201).json({
        code: 201,
        message: '创建设备成功',
        data: device
      });
    } catch (error) {
      logger.error('创建设备失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        deviceName: req.body.name,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新设备
   */
  async updateDevice(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const deviceId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const data: UpdateDeviceRequest = req.body;

      const device = await deviceService.updateDevice(deviceId, data, enterpriseId);

      res.json({
        code: 200,
        message: '更新设备成功',
        data: device
      });
    } catch (error) {
      logger.error('更新设备失败', {
        deviceId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除设备
   */
  async deleteDevice(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const deviceId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      await deviceService.deleteDevice(deviceId, enterpriseId);

      res.json({
        code: 200,
        message: '删除设备成功'
      });
    } catch (error) {
      logger.error('删除设备失败', {
        deviceId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新设备WiFi配置
   */
  async updateWifiConfig(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const deviceId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const data: UpdateWifiConfigRequest = req.body;

      const device = await deviceService.updateWifiConfig(deviceId, data, enterpriseId);

      res.json({
        code: 200,
        message: '更新WiFi配置成功',
        data: device
      });
    } catch (error) {
      logger.error('更新WiFi配置失败', {
        deviceId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取设备统计信息
   */
  async getDeviceStats(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;

      const stats = await deviceService.getDeviceStats(enterpriseId);

      res.json({
        code: 200,
        message: '获取设备统计信息成功',
        data: stats
      });
    } catch (error) {
      logger.error('获取设备统计信息失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取设备搜索选项
   */
  async getSearchOptions(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;

      const options = await deviceService.getSearchOptions(enterpriseId);

      res.json({
        code: 200,
        message: '获取设备搜索选项成功',
        data: options
      });
    } catch (error) {
      logger.error('获取设备搜索选项失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}

// 验证规则
export const deviceValidationRules = {
  // 获取设备列表验证
  getDeviceList: [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
    query('search').optional().isLength({ max: 100 }).withMessage('搜索关键词长度不能超过100个字符'),
    query('deviceModel').optional().isLength({ max: 50 }).withMessage('机型长度不能超过50个字符'),
    query('vendor').optional().isLength({ max: 50 }).withMessage('厂商长度不能超过50个字符'),
  ],

  // 获取设备详情验证
  getDeviceById: [
    param('id').isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数'),
  ],

  // 创建设备验证
  createDevice: [
    body('name').notEmpty().withMessage('设备名称不能为空').isLength({ max: 100 }).withMessage('设备名称长度不能超过100个字符'),
    body('code').optional({ checkFalsy: true }).isLength({ max: 50 }).withMessage('设备编号长度不能超过50个字符'),
    body('sn').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('设备SN长度不能超过100个字符'),
    body('mac').optional({ checkFalsy: true }).matches(/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/).withMessage('MAC地址格式不正确'),
    body('ip').optional({ checkFalsy: true }).isIP().withMessage('IP地址格式不正确'),
    body('remark').optional({ checkFalsy: true }).isLength({ max: 500 }).withMessage('备注长度不能超过500个字符'),
    body('controlModel').optional({ checkFalsy: true }).isLength({ max: 50 }).withMessage('电控型号长度不能超过50个字符'),
    body('deviceModel').optional({ checkFalsy: true }).isLength({ max: 50 }).withMessage('设备型号长度不能超过50个字符'),
    body('vendor').optional({ checkFalsy: true }).isLength({ max: 50 }).withMessage('厂商长度不能超过50个字符'),
    body('headSpace').optional().isFloat({ min: 0 }).withMessage('机头头距必须是非负数'),
    body('headNum').optional().isFloat({ min: 0 }).withMessage('机头头数必须是非负数'),
    body('headNeedleNum').optional().isInt({ min: 0 }).withMessage('机头针数必须是非负整数'),
    body('formularHeadSpace').optional().isFloat({ min: 0 }).withMessage('计算头距必须是非负数'),
    body('formularHeadNum').optional().isFloat({ min: 0 }).withMessage('计算头数必须是非负数'),
    body('formularLength').optional().isFloat({ min: 0 }).withMessage('计算长度必须是非负数'),
    body('displaySoftware').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('显示软件长度不能超过100个字符'),
    body('controlSoftware').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('主控软件长度不能超过100个字符'),
    body('productionLineId').optional({ checkFalsy: true }).isInt({ min: 1 }).withMessage('产线ID必须是大于0的整数'),
    body('registerWay').optional({ checkFalsy: true }).isLength({ max: 20 }).withMessage('添加方式长度不能超过20个字符'),
  ],

  // 更新设备验证
  updateDevice: [
    param('id').isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数'),
    body('name').optional().notEmpty().withMessage('设备名称不能为空').isLength({ max: 100 }).withMessage('设备名称长度不能超过100个字符'),
    body('code').optional({ checkFalsy: true }).isLength({ max: 50 }).withMessage('设备编号长度不能超过50个字符'),
    body('sn').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('设备SN长度不能超过100个字符'),
    body('mac').optional({ checkFalsy: true }).matches(/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/).withMessage('MAC地址格式不正确'),
    body('ip').optional({ checkFalsy: true }).isIP().withMessage('IP地址格式不正确'),
    body('remark').optional({ checkFalsy: true }).isLength({ max: 500 }).withMessage('备注长度不能超过500个字符'),
    body('controlModel').optional({ checkFalsy: true }).isLength({ max: 50 }).withMessage('电控型号长度不能超过50个字符'),
    body('deviceModel').optional({ checkFalsy: true }).isLength({ max: 50 }).withMessage('设备型号长度不能超过50个字符'),
    body('vendor').optional({ checkFalsy: true }).isLength({ max: 50 }).withMessage('厂商长度不能超过50个字符'),
    body('headSpace').optional().isFloat({ min: 0 }).withMessage('机头头距必须是非负数'),
    body('headNum').optional().isFloat({ min: 0 }).withMessage('机头头数必须是非负数'),
    body('headNeedleNum').optional().isInt({ min: 0 }).withMessage('机头针数必须是非负整数'),
    body('formularHeadSpace').optional().isFloat({ min: 0 }).withMessage('计算头距必须是非负数'),
    body('formularHeadNum').optional().isFloat({ min: 0 }).withMessage('计算头数必须是非负数'),
    body('formularLength').optional().isFloat({ min: 0 }).withMessage('计算长度必须是非负数'),
    body('displaySoftware').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('显示软件长度不能超过100个字符'),
    body('controlSoftware').optional({ checkFalsy: true }).isLength({ max: 100 }).withMessage('主控软件长度不能超过100个字符'),
    body('productionLineId').optional({ checkFalsy: true }).isInt({ min: 1 }).withMessage('产线ID必须是大于0的整数'),
    body('registerWay').optional({ checkFalsy: true }).isLength({ max: 20 }).withMessage('添加方式长度不能超过20个字符'),
  ],

  // 删除设备验证
  deleteDevice: [
    param('id').isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数'),
  ],

  // 更新WiFi配置验证
  updateWifiConfig: [
    param('id').isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数'),
    body('wifiConfig').optional().isObject().withMessage('WiFi配置必须是对象格式'),
    body('wifiConfig.bitRate').optional().isFloat({ min: 0 }).withMessage('WiFi速率必须是非负数'),
    body('wifiConfig.freq').optional().isFloat({ min: 0 }).withMessage('无线频率必须是非负数'),
    body('wifiConfig.ip').optional({ checkFalsy: true }).isIP().withMessage('WiFi IP地址格式不正确'),
    body('wifiConfig.keyMgmt').optional({ checkFalsy: true }).isLength({ max: 20 }).withMessage('WiFi加密方式长度不能超过20个字符'),
    body('wifiConfig.mac').optional({ checkFalsy: true }).matches(/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/).withMessage('WiFi MAC地址格式不正确'),
    body('wifiConfig.ssid').optional({ checkFalsy: true }).isLength({ max: 50 }).withMessage('WiFi名称长度不能超过50个字符'),
    body('wifiConfig.state').optional({ checkFalsy: true }).isLength({ max: 20 }).withMessage('WiFi状态长度不能超过20个字符'),
    body('wifiConfig.linkQuality').optional({ checkFalsy: true }).isLength({ max: 20 }).withMessage('WiFi信号质量长度不能超过20个字符'),
    body('wifiConfig.signalLevel').optional({ checkFalsy: true }).isLength({ max: 20 }).withMessage('WiFi信号强度长度不能超过20个字符'),
    body('wifiConfig.gatewayMac').optional({ checkFalsy: true }).matches(/^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/).withMessage('网关MAC地址格式不正确'),
  ],
};
