/**
 * 设备订单排序控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 18:00:00 +08:00; Reason: Shrimp Task ID: #162885ba-b132-49a9-b2df-e54d81e830d8, 创建设备排产控制器层; Principle_Applied: 控制器模式;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 13:45:00 +08:00; Reason: Shrimp Task ID: #a1d4bb38-eeaa-4d66-9e38-ceea07345003, 扩展控制器添加生产管理接口; Principle_Applied: 控制器扩展;}}
 */

import { Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import {
  DeviceOrderSequenceService,
  CreateSequenceRequest,
  UpdateSequenceRequest,
  SequenceListQuery,
  ReorderSequencesRequest,
  StartProductionRequest,
  CompleteProductionRequest,
  UpdateProgressRequest,
  ProductionHistoryQuery,
  BatchCreateSequenceRequest
} from './deviceOrderSequence.service';
import { DeviceOrderSequenceStatus } from '../../shared/database/models/DeviceOrderSequence';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

const deviceOrderSequenceService = new DeviceOrderSequenceService();

export class DeviceOrderSequenceController {
  /**
   * 创建排产记录
   */
  async createSequence(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const data: CreateSequenceRequest = {
        ...req.body,
        enterpriseId: req.user!.enterpriseId
      };

      const sequence = await deviceOrderSequenceService.createSequence(data);

      res.status(201).json({
        code: 201,
        message: '创建排产记录成功',
        data: sequence
      });
    } catch (error) {
      logger.error('创建排产记录失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        deviceId: req.body.deviceId,
        orderId: req.body.orderId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 批量创建排产记录
   */
  async batchCreateSequences(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const data: BatchCreateSequenceRequest = {
        ...req.body,
        enterpriseId: req.user!.enterpriseId
      };

      const sequences = await deviceOrderSequenceService.batchCreateSequences(data);

      res.status(201).json({
        code: 201,
        message: '批量创建排产记录成功',
        data: sequences
      });
    } catch (error) {
      logger.error('批量创建排产记录失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        orderId: req.body.orderId,
        allocationCount: req.body.allocations?.length,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新排产记录
   */
  async updateSequence(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const sequenceId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const data: UpdateSequenceRequest = req.body;

      const sequence = await deviceOrderSequenceService.updateSequence(sequenceId, data, enterpriseId);

      res.json({
        code: 200,
        message: '更新排产记录成功',
        data: sequence
      });
    } catch (error) {
      logger.error('更新排产记录失败', {
        sequenceId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除排产记录
   */
  async deleteSequence(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const sequenceId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      await deviceOrderSequenceService.deleteSequence(sequenceId, enterpriseId);

      res.json({
        code: 200,
        message: '删除排产记录成功'
      });
    } catch (error) {
      logger.error('删除排产记录失败', {
        sequenceId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据订单获取排产记录
   */
  async getSequencesByOrder(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const orderId = parseInt(req.params.orderId);
      const enterpriseId = req.user!.enterpriseId;

      const sequences = await deviceOrderSequenceService.getSequencesByOrder(orderId, enterpriseId);

      res.json({
        code: 200,
        message: '获取订单排产记录成功',
        data: sequences
      });
    } catch (error) {
      logger.error('获取订单排产记录失败', {
        orderId: req.params.orderId,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据设备获取排产队列
   */
  async getSequencesByDevice(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const deviceId = parseInt(req.params.deviceId);
      const enterpriseId = req.user!.enterpriseId;

      const sequences = await deviceOrderSequenceService.getSequencesByDevice(deviceId, enterpriseId);

      res.json({
        code: 200,
        message: '获取设备排产队列成功',
        data: sequences
      });
    } catch (error) {
      logger.error('获取设备排产队列失败', {
        deviceId: req.params.deviceId,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 重新排序排产记录
   */
  async reorderSequences(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const data: ReorderSequencesRequest = {
        ...req.body,
        enterpriseId: req.user!.enterpriseId
      };

      await deviceOrderSequenceService.reorderSequences(data);

      res.json({
        code: 200,
        message: '重新排序排产记录成功'
      });
    } catch (error) {
      logger.error('重新排序排产记录失败', {
        deviceId: req.body.deviceId,
        sequenceCount: req.body.sequenceIds?.length,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取排产记录列表
   */
  async getSequenceList(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const query: SequenceListQuery = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 10,
        deviceId: req.query.deviceId ? parseInt(req.query.deviceId as string) : undefined,
        orderId: req.query.orderId ? parseInt(req.query.orderId as string) : undefined,
        patternId: req.query.patternId ? parseInt(req.query.patternId as string) : undefined,
        status: req.query.status ? parseInt(req.query.status as string) as DeviceOrderSequenceStatus : undefined,
        enterpriseId: req.user!.enterpriseId
      };

      const result = await deviceOrderSequenceService.getSequenceList(query);

      res.json({
        code: 200,
        message: '获取排产记录列表成功',
        data: result
      });
    } catch (error) {
      logger.error('获取排产记录列表失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 计算生产数量
   */
  async calculateProductionQuantity(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const { lathesNum, deviceId, formula } = req.body;
      const enterpriseId = req.user!.enterpriseId;

      const result = await deviceOrderSequenceService.getProductionQuantityCalculation(
        lathesNum, 
        deviceId, 
        formula, 
        enterpriseId
      );

      res.json({
        code: 200,
        message: '计算生产数量成功',
        data: result
      });
    } catch (error) {
      logger.error('计算生产数量失败', {
        lathesNum: req.body.lathesNum,
        deviceId: req.body.deviceId,
        formula: req.body.formula,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 开始生产
   */
  async startProduction(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const data: StartProductionRequest = {
        sequenceId: parseInt(req.params.id),
        enterpriseId: req.user!.enterpriseId,
        operatorId: req.user!.userId,
        remark: req.body.remark
      };

      const sequence = await deviceOrderSequenceService.startProduction(data);

      res.json({
        code: 200,
        message: '开始生产成功',
        data: sequence
      });
    } catch (error) {
      logger.error('开始生产失败', {
        sequenceId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 完成生产
   */
  async completeProduction(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const data: CompleteProductionRequest = {
        sequenceId: parseInt(req.params.id),
        enterpriseId: req.user!.enterpriseId,
        operatorId: req.user!.userId,
        actualQuantity: req.body.actualQuantity,
        remark: req.body.remark
      };

      const sequence = await deviceOrderSequenceService.completeProduction(data);

      res.json({
        code: 200,
        message: '完成生产成功',
        data: sequence
      });
    } catch (error) {
      logger.error('完成生产失败', {
        sequenceId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        actualQuantity: req.body.actualQuantity,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新生产进度
   */
  async updateProgress(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const data: UpdateProgressRequest = {
        sequenceId: parseInt(req.params.id),
        enterpriseId: req.user!.enterpriseId,
        progress: req.body.progress,
        operatorId: req.user!.userId,
        remark: req.body.remark
      };

      const sequence = await deviceOrderSequenceService.updateProgress(data);

      res.json({
        code: 200,
        message: '更新生产进度成功',
        data: sequence
      });
    } catch (error) {
      logger.error('更新生产进度失败', {
        sequenceId: req.params.id,
        progress: req.body.progress,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取生产统计数据
   */
  async getProductionStatistics(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const { startDate, endDate } = req.query;
      const enterpriseId = req.user!.enterpriseId;

      const statistics = await deviceOrderSequenceService.getProductionStatistics(
        enterpriseId,
        startDate as string,
        endDate as string
      );

      res.json({
        code: 200,
        message: '获取生产统计数据成功',
        data: statistics
      });
    } catch (error) {
      logger.error('获取生产统计数据失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        startDate: req.query.startDate,
        endDate: req.query.endDate,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取生产历史记录
   */
  async getProductionHistory(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const query: ProductionHistoryQuery = {
        sequenceId: req.params.id ? parseInt(req.params.id) : undefined,
        enterpriseId: req.user!.enterpriseId,
        actionType: req.query.actionType as any,
        startDate: req.query.startDate as string,
        endDate: req.query.endDate as string,
        page: req.query.page ? parseInt(req.query.page as string) : 1,
        pageSize: req.query.pageSize ? parseInt(req.query.pageSize as string) : 20
      };

      const result = await deviceOrderSequenceService.getProductionHistory(query);

      res.json({
        code: 200,
        message: '获取生产历史记录成功',
        data: result
      });
    } catch (error) {
      logger.error('获取生产历史记录失败', {
        sequenceId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        query: req.query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}

// 参数验证规则
export const deviceOrderSequenceValidationRules = {
  // 创建排产记录验证
  createSequence: [
    body('deviceId').isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数'),
    body('orderId').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数'),
    body('patternId').isInt({ min: 1 }).withMessage('花样ID必须是大于0的整数'),
    body('formula').optional().matches(/^\d+[\u4e00-\u9fff]+=\d+头$/).withMessage('计算公式格式必须为"数字单位=数字头"'),
    body('lathesNum').optional().isInt({ min: 0 }).withMessage('车数不能为负数'),
    body('productionSequence').optional().isInt({ min: 1 }).withMessage('生产排序必须大于0')
  ],

  // 批量创建排产记录验证
  batchCreateSequences: [
    body('orderId').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数'),
    body('allocations').isArray({ min: 1 }).withMessage('分配列表不能为空'),
    body('allocations.*.deviceId').isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数'),
    body('allocations.*.patternId').isInt({ min: 1 }).withMessage('花样ID必须是大于0的整数'),
    body('allocations.*.lathesNum').isInt({ min: 1 }).withMessage('车数必须是大于0的整数'),
    body('allocations.*.formula').optional().matches(/^\d+[\u4e00-\u9fff]+=\d+头$/).withMessage('计算公式格式必须为"数字单位=数字头"')
  ],

  // 更新排产记录验证
  updateSequence: [
    param('id').isInt({ min: 1 }).withMessage('排产记录ID必须是大于0的整数'),
    body('formula').optional().matches(/^\d+[\u4e00-\u9fff]+=\d+头$/).withMessage('计算公式格式必须为"数字单位=数字头"'),
    body('lathesNum').optional().isInt({ min: 0 }).withMessage('车数不能为负数'),
    body('productionSequence').optional().isInt({ min: 1 }).withMessage('生产排序必须大于0'),
    body('status').optional().isInt({ min: 0, max: 2 }).withMessage('状态必须是有效值')
  ],

  // 删除排产记录验证
  deleteSequence: [
    param('id').isInt({ min: 1 }).withMessage('排产记录ID必须是大于0的整数')
  ],

  // 根据订单获取排产记录验证
  getSequencesByOrder: [
    param('orderId').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数')
  ],

  // 根据设备获取排产队列验证
  getSequencesByDevice: [
    param('deviceId').isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数')
  ],

  // 重新排序排产记录验证
  reorderSequences: [
    body('deviceId').isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数'),
    body('sequenceIds').isArray({ min: 1 }).withMessage('排产记录ID列表不能为空'),
    body('sequenceIds.*').isInt({ min: 1 }).withMessage('排产记录ID必须是大于0的整数')
  ],

  // 获取排产记录列表验证
  getSequenceList: [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
    query('deviceId').optional().isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数'),
    query('orderId').optional().isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数'),
    query('patternId').optional().isInt({ min: 1 }).withMessage('花样ID必须是大于0的整数'),
    query('status').optional().isInt({ min: 0, max: 2 }).withMessage('状态必须是有效值')
  ],

  // 计算生产数量验证
  calculateProductionQuantity: [
    body('lathesNum').isInt({ min: 0 }).withMessage('车数不能为负数'),
    body('deviceId').isInt({ min: 1 }).withMessage('设备ID必须是大于0的整数'),
    body('formula').matches(/^\d+[\u4e00-\u9fff]+=\d+头$/).withMessage('计算公式格式必须为"数字单位=数字头"')
  ],

  // 开始生产验证
  startProduction: [
    param('id').isInt({ min: 1 }).withMessage('排产记录ID必须是大于0的整数'),
    body('remark').optional().isString().withMessage('备注必须是字符串')
  ],

  // 完成生产验证
  completeProduction: [
    param('id').isInt({ min: 1 }).withMessage('排产记录ID必须是大于0的整数'),
    body('actualQuantity').optional().isInt({ min: 0 }).withMessage('实际生产数量不能为负数'),
    body('remark').optional().isString().withMessage('备注必须是字符串')
  ],

  // 更新生产进度验证
  updateProgress: [
    param('id').isInt({ min: 1 }).withMessage('排产记录ID必须是大于0的整数'),
    body('progress').isInt({ min: 0, max: 100 }).withMessage('生产进度必须是0-100之间的整数'),
    body('remark').optional().isString().withMessage('备注必须是字符串')
  ],

  // 获取生产统计验证
  getProductionStatistics: [
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确')
  ],

  // 获取生产历史验证
  getProductionHistory: [
    param('id').optional().isInt({ min: 1 }).withMessage('排产记录ID必须是大于0的整数'),
    query('actionType').optional().isString().withMessage('操作类型必须是字符串'),
    query('startDate').optional().isISO8601().withMessage('开始日期格式不正确'),
    query('endDate').optional().isISO8601().withMessage('结束日期格式不正确'),
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数')
  ]
};
