/**
 * 订单控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 17:45:00 +08:00; Reason: Shrimp Task ID: #a6d9a76c-b9ae-4e64-a7f7-9db88f107b29, 创建订单控制器层; Principle_Applied: 控制器模式;}}
 */

import { Request, Response } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { 
  OrderService, 
  OrderListQuery, 
  CreateOrderRequest, 
  UpdateOrderRequest,
  UpdateOrderStatusRequest 
} from './order.service';
import { OrderStatus, OrderType } from '../../shared/database/models/Order';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

const orderService = new OrderService();

export class OrderController {
  /**
   * 获取订单列表
   */
  async getOrderList(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const query: OrderListQuery = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 10,
        search: req.query.search as string,
        status: req.query.status ? parseInt(req.query.status as string) as OrderStatus : undefined,
        type: req.query.type ? parseInt(req.query.type as string) as OrderType : undefined,
        customerName: req.query.customerName as string,
        salesman: req.query.salesman as string,
        orderDateStart: req.query.orderDateStart as string,
        orderDateEnd: req.query.orderDateEnd as string,
        deliveryDateStart: req.query.deliveryDateStart as string,
        deliveryDateEnd: req.query.deliveryDateEnd as string,
        enterpriseId: req.user!.enterpriseId
      };

      const result = await orderService.getOrderList(query);

      res.json({
        code: 200,
        message: '获取订单列表成功',
        data: result
      });
    } catch (error) {
      logger.error('获取订单列表失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据ID获取订单详情
   */
  async getOrderById(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const orderId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      const order = await orderService.getOrderById(orderId, enterpriseId);

      res.json({
        code: 200,
        message: '获取订单详情成功',
        data: order
      });
    } catch (error) {
      logger.error('获取订单详情失败', {
        orderId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建订单
   */
  async createOrder(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const data: CreateOrderRequest = {
        ...req.body,
        enterpriseId: req.user!.enterpriseId
      };

      // 处理日期字段
      if (data.orderDate) {
        data.orderDate = new Date(data.orderDate);
      }
      if (data.deliveryDate) {
        data.deliveryDate = new Date(data.deliveryDate);
      }

      const order = await orderService.createOrder(data);

      res.status(201).json({
        code: 201,
        message: '创建订单成功',
        data: order
      });
    } catch (error) {
      logger.error('创建订单失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        orderCode: req.body.code,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新订单
   */
  async updateOrder(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const orderId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const data: UpdateOrderRequest = req.body;

      // 处理日期字段
      if (data.orderDate) {
        data.orderDate = new Date(data.orderDate);
      }
      if (data.deliveryDate) {
        data.deliveryDate = new Date(data.deliveryDate);
      }

      const order = await orderService.updateOrder(orderId, data, enterpriseId);

      res.json({
        code: 200,
        message: '更新订单成功',
        data: order
      });
    } catch (error) {
      logger.error('更新订单失败', {
        orderId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除订单
   */
  async deleteOrder(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const orderId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;

      await orderService.deleteOrder(orderId, enterpriseId);

      res.json({
        code: 200,
        message: '删除订单成功'
      });
    } catch (error) {
      logger.error('删除订单失败', {
        orderId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        throw createApiError('参数验证失败', 400, 'VALIDATION_ERROR', errors.array());
      }

      const orderId = parseInt(req.params.id);
      const enterpriseId = req.user!.enterpriseId;
      const data: UpdateOrderStatusRequest = req.body;

      const order = await orderService.updateOrderStatus(orderId, data, enterpriseId);

      res.json({
        code: 200,
        message: '更新订单状态成功',
        data: order
      });
    } catch (error) {
      logger.error('更新订单状态失败', {
        orderId: req.params.id,
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        status: req.body.status,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取搜索选项
   */
  async getSearchOptions(req: Request, res: Response): Promise<void> {
    try {
      const enterpriseId = req.user!.enterpriseId;

      const options = await orderService.getSearchOptions(enterpriseId);

      res.json({
        code: 200,
        message: '获取搜索选项成功',
        data: options
      });
    } catch (error) {
      logger.error('获取搜索选项失败', {
        userId: req.user?.userId,
        enterpriseId: req.user?.enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}

// 参数验证规则
export const orderValidationRules = {
  // 获取订单列表验证
  getOrderList: [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
    query('status').optional().isInt({ min: 0, max: 3 }).withMessage('状态必须是有效值'),
    query('type').optional().isInt({ min: 1, max: 4 }).withMessage('类型必须是有效值'),
    query('orderDateStart').optional({ values: 'falsy' }).isISO8601().withMessage('下单开始日期格式不正确'),
    query('orderDateEnd').optional({ values: 'falsy' }).isISO8601().withMessage('下单结束日期格式不正确'),
    query('deliveryDateStart').optional({ values: 'falsy' }).isISO8601().withMessage('交货开始日期格式不正确'),
    query('deliveryDateEnd').optional({ values: 'falsy' }).isISO8601().withMessage('交货结束日期格式不正确')
  ],

  // 获取订单详情验证
  getOrderById: [
    param('id').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数')
  ],

  // 创建订单验证
  createOrder: [
    body('type').optional().isInt({ min: 1, max: 3 }).withMessage('订单类型必须是有效值'),
    body('code').optional().isLength({ min: 1, max: 100 }).withMessage('订单编号长度必须在1-100个字符之间'),
    body('customerName').optional().isLength({ min: 1, max: 200 }).withMessage('客户名称长度必须在1-200个字符之间'),
    body('salesman').optional().isLength({ min: 1, max: 100 }).withMessage('业务员名称长度必须在1-100个字符之间'),
    body('orderDate').optional().isISO8601().withMessage('下单日期格式不正确'),
    body('deliveryDate').optional().isISO8601().withMessage('交货日期格式不正确'),
    body('orderUnitId').optional().isInt({ min: 1 }).withMessage('记量单位ID必须是大于0的整数'),
    body('orderQuantity').optional().isInt({ min: 0 }).withMessage('订单量不能为负数'),
    body('orderFiles').optional().isArray().withMessage('订单文件必须是数组格式'),
    body('orderFiles.*').optional().isString().withMessage('订单文件路径必须是字符串'),
    body('patternInfo').optional().isArray().withMessage('花样信息必须是数组格式'),
    body('patternInfo.*.patternId').if(body('patternInfo').exists()).notEmpty().withMessage('花样ID不能为空'),
    body('patternInfo.*.patternQuantity').if(body('patternInfo').exists()).isInt({ min: 1 }).withMessage('花样数量必须大于0')
  ],

  // 更新订单验证
  updateOrder: [
    param('id').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数'),
    body('type').optional().isInt({ min: 1, max: 3 }).withMessage('订单类型必须是有效值'),
    body('code').optional().isLength({ min: 1, max: 100 }).withMessage('订单编号长度必须在1-100个字符之间'),
    body('customerName').optional().isLength({ min: 1, max: 200 }).withMessage('客户名称长度必须在1-200个字符之间'),
    body('salesman').optional().isLength({ min: 1, max: 100 }).withMessage('业务员名称长度必须在1-100个字符之间'),
    body('orderDate').optional().isISO8601().withMessage('下单日期格式不正确'),
    body('deliveryDate').optional().isISO8601().withMessage('交货日期格式不正确'),
    body('orderUnitId').optional().isInt({ min: 1 }).withMessage('记量单位ID必须是大于0的整数'),
    body('orderQuantity').optional().isInt({ min: 0 }).withMessage('订单量不能为负数'),
    body('orderFiles').optional().isArray().withMessage('订单文件必须是数组格式'),
    body('orderFiles.*').optional().isString().withMessage('订单文件路径必须是字符串'),
    body('patternInfo').optional().isArray().withMessage('花样信息必须是数组格式'),
    body('patternInfo.*.patternId').if(body('patternInfo').exists()).notEmpty().withMessage('花样ID不能为空'),
    body('patternInfo.*.patternQuantity').if(body('patternInfo').exists()).isInt({ min: 1 }).withMessage('花样数量必须大于0')
  ],

  // 删除订单验证
  deleteOrder: [
    param('id').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数')
  ],

  // 更新订单状态验证
  updateOrderStatus: [
    param('id').isInt({ min: 1 }).withMessage('订单ID必须是大于0的整数'),
    body('status').isInt({ min: 0, max: 3 }).withMessage('状态必须是有效值'),
    body('remark').optional().isString().withMessage('备注必须是字符串')
  ]
};
