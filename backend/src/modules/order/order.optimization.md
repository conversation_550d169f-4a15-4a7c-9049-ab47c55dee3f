# 订单管理模块优化配置

## 数据库优化

### 1. 索引优化
```sql
-- 订单表索引
CREATE INDEX idx_order_enterprise_status ON em_orders(enterprise_id, status);
CREATE INDEX idx_order_enterprise_customer ON em_orders(enterprise_id, customer_name);
CREATE INDEX idx_order_enterprise_salesman ON em_orders(enterprise_id, salesman);
CREATE INDEX idx_order_enterprise_dates ON em_orders(enterprise_id, order_date, delivery_date);
CREATE INDEX idx_order_code ON em_orders(code);

-- 设备排产表索引
CREATE INDEX idx_sequence_enterprise_device ON em_device_order_sequences(enterprise_id, device_id);
CREATE INDEX idx_sequence_enterprise_order ON em_device_order_sequences(enterprise_id, order_id);
CREATE INDEX idx_sequence_enterprise_pattern ON em_device_order_sequences(enterprise_id, pattern_id);
CREATE INDEX idx_sequence_device_sequence ON em_device_order_sequences(device_id, production_sequence);
CREATE INDEX idx_sequence_status ON em_device_order_sequences(status);
```

### 2. 查询优化
```typescript
// 订单列表查询优化
const optimizedOrderQuery = {
  attributes: [
    'id', 'code', 'customerName', 'salesman', 
    'type', 'status', 'orderDate', 'deliveryDate',
    'orderQuantity', 'createdAt', 'updatedAt'
  ],
  include: [
    {
      model: DeviceOrderSequence,
      attributes: ['id', 'status', 'productionQuantity'],
      required: false
    }
  ],
  order: [['createdAt', 'DESC']],
  limit: pageSize,
  offset: (page - 1) * pageSize
};

// 排产查询优化
const optimizedSequenceQuery = {
  attributes: [
    'id', 'deviceId', 'orderId', 'patternId',
    'lathesNum', 'productionQuantity', 'productionSequence',
    'status', 'formula', 'createdAt'
  ],
  include: [
    {
      model: Device,
      attributes: ['id', 'name', 'code', 'headNum']
    },
    {
      model: Order,
      attributes: ['id', 'code', 'customerName', 'deliveryDate']
    },
    {
      model: Pattern,
      attributes: ['id', 'name', 'code']
    }
  ],
  order: [['productionSequence', 'ASC']]
};
```

## 缓存策略

### 1. Redis缓存配置
```typescript
// 缓存键定义
const CACHE_KEYS = {
  ORDER_LIST: (enterpriseId: number, page: number, filters: string) => 
    `order:list:${enterpriseId}:${page}:${filters}`,
  ORDER_DETAIL: (orderId: number) => `order:detail:${orderId}`,
  SEQUENCE_LIST: (orderId: number) => `sequence:order:${orderId}`,
  SEARCH_OPTIONS: (enterpriseId: number) => `order:options:${enterpriseId}`
};

// 缓存时间配置
const CACHE_TTL = {
  ORDER_LIST: 300, // 5分钟
  ORDER_DETAIL: 600, // 10分钟
  SEQUENCE_LIST: 180, // 3分钟
  SEARCH_OPTIONS: 1800 // 30分钟
};
```

### 2. 缓存实现
```typescript
// 订单列表缓存
export class OrderCacheService {
  async getOrderList(query: OrderListQuery): Promise<OrderListResponse | null> {
    const cacheKey = CACHE_KEYS.ORDER_LIST(
      query.enterpriseId, 
      query.page || 1, 
      JSON.stringify(query)
    );
    
    const cached = await redis.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
    
    return null;
  }
  
  async setOrderList(query: OrderListQuery, data: OrderListResponse): Promise<void> {
    const cacheKey = CACHE_KEYS.ORDER_LIST(
      query.enterpriseId, 
      query.page || 1, 
      JSON.stringify(query)
    );
    
    await redis.setex(cacheKey, CACHE_TTL.ORDER_LIST, JSON.stringify(data));
  }
  
  async invalidateOrderCache(enterpriseId: number): Promise<void> {
    const pattern = `order:*:${enterpriseId}:*`;
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  }
}
```

## 性能监控

### 1. 查询性能监控
```typescript
// 查询时间监控装饰器
export function MonitorQuery(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value;
  
  descriptor.value = async function (...args: any[]) {
    const startTime = Date.now();
    
    try {
      const result = await method.apply(this, args);
      const duration = Date.now() - startTime;
      
      // 记录查询性能
      logger.info('Query performance', {
        method: `${target.constructor.name}.${propertyName}`,
        duration,
        args: args.length
      });
      
      // 慢查询告警
      if (duration > 1000) {
        logger.warn('Slow query detected', {
          method: `${target.constructor.name}.${propertyName}`,
          duration,
          args
        });
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error('Query error', {
        method: `${target.constructor.name}.${propertyName}`,
        duration,
        error: error.message
      });
      throw error;
    }
  };
}
```

### 2. 业务指标监控
```typescript
// 业务指标收集
export class OrderMetricsService {
  async recordOrderCreation(enterpriseId: number): Promise<void> {
    await metrics.increment('order.created', 1, { enterpriseId });
  }
  
  async recordSequenceCreation(enterpriseId: number, deviceId: number): Promise<void> {
    await metrics.increment('sequence.created', 1, { enterpriseId, deviceId });
  }
  
  async recordQueryPerformance(operation: string, duration: number): Promise<void> {
    await metrics.histogram('query.duration', duration, { operation });
  }
}
```

## 错误处理优化

### 1. 统一错误处理
```typescript
// 业务错误类型
export enum OrderErrorCode {
  ORDER_NOT_FOUND = 'ORDER_NOT_FOUND',
  ORDER_CANNOT_EDIT = 'ORDER_CANNOT_EDIT',
  ORDER_CANNOT_DELETE = 'ORDER_CANNOT_DELETE',
  SEQUENCE_CONFLICT = 'SEQUENCE_CONFLICT',
  INVALID_FORMULA = 'INVALID_FORMULA'
}

// 错误处理中间件
export const orderErrorHandler = (error: any, req: Request, res: Response, next: NextFunction) => {
  if (error.code && Object.values(OrderErrorCode).includes(error.code)) {
    return res.status(error.statusCode || 400).json({
      code: error.statusCode || 400,
      message: error.message,
      errorCode: error.code,
      details: error.details
    });
  }
  
  next(error);
};
```

### 2. 重试机制
```typescript
// 重试装饰器
export function Retry(maxAttempts: number = 3, delay: number = 1000) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      let lastError: any;
      
      for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
          return await method.apply(this, args);
        } catch (error) {
          lastError = error;
          
          if (attempt < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, delay * attempt));
            logger.warn(`Retry attempt ${attempt} for ${propertyName}`, { error: error.message });
          }
        }
      }
      
      throw lastError;
    };
  };
}
```

## 安全优化

### 1. 输入验证增强
```typescript
// 高级验证规则
export const advancedOrderValidation = {
  code: [
    { required: true, message: '订单编号不能为空' },
    { pattern: /^[A-Z0-9\-_]{3,50}$/, message: '订单编号格式不正确' },
    { 
      validator: async (value: string, enterpriseId: number) => {
        const exists = await Order.findOne({ 
          where: { code: value, enterpriseId } 
        });
        if (exists) {
          throw new Error('订单编号已存在');
        }
      }
    }
  ],
  
  formula: [
    { pattern: /^\d+件=\d+头$/, message: '计算公式格式必须为"数字件=数字头"' },
    {
      validator: (value: string) => {
        const match = value.match(/^(\d+)件=(\d+)头$/);
        if (match) {
          const pieces = parseInt(match[1]);
          const heads = parseInt(match[2]);
          if (pieces <= 0 || heads <= 0) {
            throw new Error('计算公式中的数值必须大于0');
          }
          if (pieces > 10000 || heads > 1000) {
            throw new Error('计算公式中的数值超出合理范围');
          }
        }
      }
    }
  ]
};
```

### 2. 权限控制增强
```typescript
// 细粒度权限检查
export class OrderPermissionService {
  async canEditOrder(userId: number, orderId: number): Promise<boolean> {
    const order = await Order.findByPk(orderId);
    if (!order) return false;
    
    // 检查用户是否属于同一企业
    const user = await User.findByPk(userId);
    if (user?.enterpriseId !== order.enterpriseId) return false;
    
    // 检查订单状态是否允许编辑
    if (!order.canEdit()) return false;
    
    // 检查用户权限
    return await this.hasPermission(userId, 'order:update');
  }
  
  async canDeleteOrder(userId: number, orderId: number): Promise<boolean> {
    const order = await Order.findByPk(orderId);
    if (!order) return false;
    
    // 只有未排产的订单才能删除
    if (order.status !== OrderStatus.UNSCHEDULED) return false;
    
    return await this.canEditOrder(userId, orderId);
  }
}
```

## 部署配置

### 1. 环境变量
```bash
# 订单模块配置
ORDER_CACHE_TTL=300
ORDER_MAX_FILE_SIZE=10485760
ORDER_ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx
ORDER_UPLOAD_PATH=/uploads/orders
ORDER_QUERY_TIMEOUT=30000

# 性能配置
ORDER_LIST_PAGE_SIZE=20
ORDER_MAX_BATCH_SIZE=100
ORDER_SEARCH_DEBOUNCE=300
```

### 2. Nginx配置
```nginx
# 文件上传优化
location /api/v1/upload/order {
    client_max_body_size 10M;
    proxy_read_timeout 60s;
    proxy_send_timeout 60s;
}

# 静态文件缓存
location /uploads/orders {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 监控告警

### 1. 关键指标
- 订单创建成功率
- 排产操作响应时间
- 文件上传成功率
- 数据库查询性能
- 缓存命中率

### 2. 告警规则
- 查询响应时间 > 2秒
- 错误率 > 5%
- 缓存命中率 < 80%
- 文件上传失败率 > 10%
