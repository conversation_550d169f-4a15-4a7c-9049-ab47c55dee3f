/**
 * 订单服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 17:15:00 +08:00; Reason: Shrimp Task ID: #94575596-485d-4ed1-8dbf-6f714320114a, 创建订单业务服务层; Principle_Applied: 业务逻辑分离;}}
 */

import { Op, WhereOptions } from 'sequelize';
import { Order, OrderStatus, OrderType, PatternInfo } from '../../shared/database/models/Order';
import { DeviceOrderSequence } from '../../shared/database/models/DeviceOrderSequence';
import { Pattern } from '../../shared/database/models/Pattern';
import { Device } from '../../shared/database/models/Device';
import { createApiError } from '../../shared/middleware/error.middleware';
import { logger } from '../../shared/utils/logger';

// 订单列表查询参数
export interface OrderListQuery {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: OrderStatus;
  type?: OrderType;
  customerName?: string;
  salesman?: string;
  orderDateStart?: string;
  orderDateEnd?: string;
  deliveryDateStart?: string;
  deliveryDateEnd?: string;
  enterpriseId: number;
}

// 订单列表响应
export interface OrderListResponse {
  orders: Order[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 搜索选项接口
export interface OrderSearchOptions {
  customers: Array<{ name: string; count: number }>;
  salesmen: Array<{ name: string; count: number }>;
  patterns: Array<{ id: number; name: string; code?: string }>;
  devices: Array<{ id: number; name: string; code?: string }>;
}

// 创建订单请求
export interface CreateOrderRequest {
  enterpriseId: number;
  type?: OrderType;
  code?: string;
  customerName?: string;
  salesman?: string;
  orderDate?: Date;
  deliveryDate?: Date;
  orderUnitId?: number;
  orderQuantity?: number;
  remark?: string;
  orderFiles?: string[];
  patternInfo?: PatternInfo[];
}

// 更新订单请求
export interface UpdateOrderRequest {
  type?: OrderType;
  code?: string;
  customerName?: string;
  salesman?: string;
  orderDate?: Date;
  deliveryDate?: Date;
  orderUnitId?: number;
  orderQuantity?: number;
  remark?: string;
  orderFiles?: string[];
  patternInfo?: PatternInfo[];
}

// 订单状态更新请求
export interface UpdateOrderStatusRequest {
  status: OrderStatus;
  remark?: string;
}

export class OrderService {
  /**
   * 获取订单列表
   */
  async getOrderList(query: OrderListQuery): Promise<OrderListResponse> {
    try {
      const {
        page = 1,
        pageSize = 10,
        search,
        status,
        type,
        customerName,
        salesman,
        orderDateStart,
        orderDateEnd,
        deliveryDateStart,
        deliveryDateEnd,
        enterpriseId
      } = query;

      // 构建查询条件
      const whereConditions: WhereOptions = {
        enterpriseId
      };

      // 搜索条件
      if (search) {
        whereConditions[Op.or] = [
          { code: { [Op.iLike]: `%${search}%` } },
          { customerName: { [Op.iLike]: `%${search}%` } },
          { salesman: { [Op.iLike]: `%${search}%` } },
          { remark: { [Op.iLike]: `%${search}%` } }
        ];
      }

      // 筛选条件
      if (status !== undefined) {
        whereConditions.status = status;
      }
      if (type !== undefined) {
        whereConditions.type = type;
      }
      if (customerName) {
        whereConditions.customerName = { [Op.iLike]: `%${customerName}%` };
      }
      if (salesman) {
        whereConditions.salesman = { [Op.iLike]: `%${salesman}%` };
      }

      // 日期范围筛选
      if (orderDateStart || orderDateEnd) {
        const dateCondition: any = {};
        if (orderDateStart) {
          dateCondition[Op.gte] = new Date(orderDateStart);
        }
        if (orderDateEnd) {
          dateCondition[Op.lte] = new Date(orderDateEnd);
        }
        whereConditions.orderDate = dateCondition;
      }

      if (deliveryDateStart || deliveryDateEnd) {
        const dateCondition: any = {};
        if (deliveryDateStart) {
          dateCondition[Op.gte] = new Date(deliveryDateStart);
        }
        if (deliveryDateEnd) {
          dateCondition[Op.lte] = new Date(deliveryDateEnd);
        }
        whereConditions.deliveryDate = dateCondition;
      }

      // 计算偏移量
      const offset = (page - 1) * pageSize;

      // 查询订单列表
      const { rows: orders, count: total } = await Order.findAndCountAll({
        where: whereConditions,
        include: [
          {
            association: 'deviceOrderSequences',
            attributes: ['id', 'deviceId', 'patternId', 'status', 'productionSequence'],
            required: false,
            include: [
              {
                association: 'device',
                attributes: ['id', 'name', 'code'],
                required: false,
              },
              {
                association: 'pattern',
                attributes: ['id', 'name', 'code'],
                required: false,
              }
            ]
          }
        ],
        order: [['createdAt', 'DESC']],
        limit: pageSize,
        offset,
        distinct: true
      });

      const totalPages = Math.ceil(total / pageSize);

      logger.info('获取订单列表成功', {
        total,
        page,
        pageSize,
        enterpriseId
      });

      return {
        orders,
        total,
        page,
        pageSize,
        totalPages
      };
    } catch (error) {
      logger.error('获取订单列表失败', {
        query,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 根据ID获取订单详情
   */
  async getOrderById(id: number, enterpriseId: number): Promise<Order> {
    try {
      const order = await Order.findOne({
        where: { id, enterpriseId },
        include: [
          {
            association: 'deviceOrderSequences',
            include: [
              {
                association: 'device',
                attributes: ['id', 'name', 'code', 'headNum'],
                required: false,
              },
              {
                association: 'pattern',
                attributes: ['id', 'name', 'code'],
                required: false,
              }
            ],
            order: [['productionSequence', 'ASC']]
          }
        ]
      });

      if (!order) {
        throw createApiError('订单不存在', 404, 'ORDER_NOT_FOUND');
      }

      logger.info('获取订单详情成功', { orderId: id, enterpriseId });

      return order;
    } catch (error) {
      logger.error('获取订单详情失败', {
        orderId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 创建订单
   */
  async createOrder(data: CreateOrderRequest): Promise<Order> {
    try {
      // 检查订单编号是否重复
      if (data.code) {
        const existingOrder = await Order.findOne({
          where: {
            code: data.code,
            enterpriseId: data.enterpriseId
          }
        });

        if (existingOrder) {
          throw createApiError('订单编号已存在', 400, 'ORDER_CODE_EXISTS');
        }
      }

      // 验证花样信息
      if (data.patternInfo && data.patternInfo.length > 0) {
        const patternIds = data.patternInfo.map(p => p.patternId);
        const patterns = await Pattern.findAll({
          where: {
            id: { [Op.in]: patternIds },
            enterpriseId: data.enterpriseId
          }
        });

        if (patterns.length !== patternIds.length) {
          throw createApiError('部分花样不存在或不属于当前企业', 400, 'INVALID_PATTERN_INFO');
        }

        // 计算订单总量
        const totalQuantity = data.patternInfo.reduce((sum, item) => sum + item.patternQuantity, 0);
        if (!data.orderQuantity) {
          data.orderQuantity = totalQuantity;
        }
      }

      // 创建订单
      const order = await Order.create({
        ...data,
        status: OrderStatus.UNSCHEDULED
      });

      logger.info('创建订单成功', {
        orderId: order.id,
        orderCode: order.code,
        enterpriseId: data.enterpriseId
      });

      return order;
    } catch (error) {
      logger.error('创建订单失败', {
        data,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新订单
   */
  async updateOrder(id: number, data: UpdateOrderRequest, enterpriseId: number): Promise<Order> {
    try {
      const order = await Order.findOne({
        where: { id, enterpriseId }
      });

      if (!order) {
        throw createApiError('订单不存在', 404, 'ORDER_NOT_FOUND');
      }

      // 检查是否可以编辑
      if (!order.canEdit()) {
        throw createApiError('当前状态下不允许编辑订单', 400, 'ORDER_CANNOT_EDIT');
      }

      // 检查订单编号是否重复
      if (data.code && data.code !== order.code) {
        const existingOrder = await Order.findOne({
          where: {
            code: data.code,
            enterpriseId,
            id: { [Op.ne]: id }
          }
        });

        if (existingOrder) {
          throw createApiError('订单编号已存在', 400, 'ORDER_CODE_EXISTS');
        }
      }

      // 验证花样信息
      if (data.patternInfo && data.patternInfo.length > 0) {
        const patternIds = data.patternInfo.map(p => p.patternId);
        const patterns = await Pattern.findAll({
          where: {
            id: { [Op.in]: patternIds },
            enterpriseId
          }
        });

        if (patterns.length !== patternIds.length) {
          throw createApiError('部分花样不存在或不属于当前企业', 400, 'INVALID_PATTERN_INFO');
        }

        // 重新计算订单总量
        const totalQuantity = data.patternInfo.reduce((sum, item) => sum + item.patternQuantity, 0);
        if (!data.orderQuantity) {
          data.orderQuantity = totalQuantity;
        }
      }

      // 更新订单
      await order.update(data);

      logger.info('更新订单成功', {
        orderId: id,
        enterpriseId
      });

      return order;
    } catch (error) {
      logger.error('更新订单失败', {
        orderId: id,
        data,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 删除订单
   */
  async deleteOrder(id: number, enterpriseId: number): Promise<void> {
    try {
      const order = await Order.findOne({
        where: { id, enterpriseId }
      });

      if (!order) {
        throw createApiError('订单不存在', 404, 'ORDER_NOT_FOUND');
      }

      // 检查是否可以删除
      if (!order.canDelete()) {
        throw createApiError('当前状态下不允许删除订单', 400, 'ORDER_CANNOT_DELETE');
      }

      // 检查是否有关联的设备排产
      const sequenceCount = await DeviceOrderSequence.count({
        where: { orderId: id, enterpriseId }
      });

      if (sequenceCount > 0) {
        throw createApiError('订单已有设备排产，无法删除', 400, 'ORDER_HAS_SEQUENCES');
      }

      // 删除订单
      await order.destroy();

      logger.info('删除订单成功', {
        orderId: id,
        enterpriseId
      });
    } catch (error) {
      logger.error('删除订单失败', {
        orderId: id,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(id: number, data: UpdateOrderStatusRequest, enterpriseId: number): Promise<Order> {
    try {
      const order = await Order.findOne({
        where: { id, enterpriseId }
      });

      if (!order) {
        throw createApiError('订单不存在', 404, 'ORDER_NOT_FOUND');
      }

      // 验证状态流转
      const currentStatus = order.status;
      const newStatus = data.status;

      // 定义允许的状态流转
      const allowedTransitions: Record<OrderStatus, OrderStatus[]> = {
        [OrderStatus.UNSCHEDULED]: [OrderStatus.SCHEDULED, OrderStatus.COMPLETED],
        [OrderStatus.SCHEDULED]: [OrderStatus.IN_PRODUCTION, OrderStatus.UNSCHEDULED],
        [OrderStatus.IN_PRODUCTION]: [OrderStatus.COMPLETED, OrderStatus.SCHEDULED],
        [OrderStatus.COMPLETED]: [] // 已完成状态不允许流转
      };

      if (!allowedTransitions[currentStatus].includes(newStatus)) {
        throw createApiError(
          `不允许从${order.getStatusName()}状态流转到${Order.prototype.getStatusName.call({ status: newStatus })}状态`,
          400,
          'INVALID_STATUS_TRANSITION'
        );
      }

      // 更新状态
      await order.update({
        status: newStatus
      });

      logger.info('更新订单状态成功', {
        orderId: id,
        oldStatus: currentStatus,
        newStatus,
        enterpriseId
      });

      return order;
    } catch (error) {
      logger.error('更新订单状态失败', {
        orderId: id,
        data,
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取搜索选项
   */
  async getSearchOptions(enterpriseId: number): Promise<OrderSearchOptions> {
    try {
      // 获取客户列表
      const customerResult = await Order.findAll({
        where: {
          enterpriseId,
          customerName: { [Op.ne]: null }
        },
        attributes: [
          'customerName',
          [Order.sequelize!.fn('COUNT', Order.sequelize!.col('id')), 'count']
        ],
        group: ['customerName'],
        order: [[Order.sequelize!.fn('COUNT', Order.sequelize!.col('id')), 'DESC']],
        limit: 20,
        raw: true
      }) as any[];

      const customers = customerResult.map(item => ({
        name: item.customerName,
        count: parseInt(item.count)
      }));

      // 获取业务员列表
      const salesmanResult = await Order.findAll({
        where: {
          enterpriseId,
          salesman: { [Op.ne]: null }
        },
        attributes: [
          'salesman',
          [Order.sequelize!.fn('COUNT', Order.sequelize!.col('id')), 'count']
        ],
        group: ['salesman'],
        order: [[Order.sequelize!.fn('COUNT', Order.sequelize!.col('id')), 'DESC']],
        limit: 20,
        raw: true
      }) as any[];

      const salesmen = salesmanResult.map(item => ({
        name: item.salesman,
        count: parseInt(item.count)
      }));

      // 获取花样列表
      const patterns = await Pattern.findAll({
        where: { enterpriseId },
        attributes: ['id', 'name', 'code'],
        order: [['name', 'ASC']],
        limit: 100
      });

      // 获取设备列表
      const devices = await Device.findAll({
        where: { enterpriseId },
        attributes: ['id', 'name', 'code'],
        order: [['name', 'ASC']],
        limit: 100
      });

      logger.info('获取搜索选项成功', { enterpriseId });

      return {
        customers,
        salesmen,
        patterns: patterns.map(p => ({ id: p.id, name: p.name, code: p.code })),
        devices: devices.map(d => ({ id: d.id, name: d.name, code: d.code }))
      };
    } catch (error) {
      logger.error('获取搜索选项失败', {
        enterpriseId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }
}
