/**
 * 设备订单排序路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 18:30:00 +08:00; Reason: Shrimp Task ID: #64d308e1-c835-4f08-97a8-7f99f430939a, 创建设备排产路由配置; Principle_Applied: 路由分离;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 13:50:00 +08:00; Reason: Shrimp Task ID: #a6820d94-7f1a-408c-97f4-160070b659ea, 扩展路由添加生产管理接口; Principle_Applied: RESTful API设计;}}
 */

import { Router } from 'express';
import { DeviceOrderSequenceController, deviceOrderSequenceValidationRules } from './deviceOrderSequence.controller';
import { authMiddleware, permissionMiddleware } from '../../shared/middleware/auth.middleware';
import { asyncHandler } from '../../shared/middleware/async.middleware';

const router = Router();
const deviceOrderSequenceController = new DeviceOrderSequenceController();

/**
 * @swagger
 * components:
 *   schemas:
 *     DeviceOrderSequence:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 排产记录ID
 *         enterpriseId:
 *           type: integer
 *           description: 企业ID
 *         deviceId:
 *           type: integer
 *           description: 设备ID
 *         orderId:
 *           type: integer
 *           description: 订单ID
 *         patternId:
 *           type: integer
 *           description: 花样ID
 *         formula:
 *           type: string
 *           description: 计算公式
 *           example: "1件=1头"
 *         lathesNum:
 *           type: integer
 *           description: 车数
 *         productionQuantity:
 *           type: integer
 *           description: 生产数量
 *         productionSequence:
 *           type: integer
 *           description: 生产排序
 *         status:
 *           type: integer
 *           description: 状态
 *           enum: [0, 1, 2]
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 */

/**
 * @swagger
 * /api/v1/sequences:
 *   get:
 *     summary: 获取排产记录列表
 *     tags: [设备排产]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *       - in: query
 *         name: deviceId
 *         schema:
 *           type: integer
 *         description: 设备ID
 *       - in: query
 *         name: orderId
 *         schema:
 *           type: integer
 *         description: 订单ID
 *       - in: query
 *         name: patternId
 *         schema:
 *           type: integer
 *         description: 花样ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2]
 *         description: 状态
 *     responses:
 *       200:
 *         description: 获取排产记录列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取排产记录列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     sequences:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DeviceOrderSequence'
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 */
router.get('/', 
  authMiddleware, 
  permissionMiddleware(['production:view']), 
  deviceOrderSequenceValidationRules.getSequenceList, 
  asyncHandler(deviceOrderSequenceController.getSequenceList)
);

/**
 * @swagger
 * /api/v1/sequences:
 *   post:
 *     summary: 创建排产记录
 *     tags: [设备排产]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - deviceId
 *               - orderId
 *               - patternId
 *             properties:
 *               deviceId:
 *                 type: integer
 *                 description: 设备ID
 *               orderId:
 *                 type: integer
 *                 description: 订单ID
 *               patternId:
 *                 type: integer
 *                 description: 花样ID
 *               formula:
 *                 type: string
 *                 description: 计算公式
 *                 example: "1件=1头"
 *               lathesNum:
 *                 type: integer
 *                 description: 车数
 *               productionSequence:
 *                 type: integer
 *                 description: 生产排序
 *     responses:
 *       201:
 *         description: 创建排产记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 创建排产记录成功
 *                 data:
 *                   $ref: '#/components/schemas/DeviceOrderSequence'
 */
router.post('/', 
  authMiddleware, 
  permissionMiddleware(['production:create']), 
  deviceOrderSequenceValidationRules.createSequence, 
  asyncHandler(deviceOrderSequenceController.createSequence)
);

/**
 * @swagger
 * /api/v1/sequences/batch:
 *   post:
 *     summary: 批量创建排产记录
 *     tags: [设备排产]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - orderId
 *               - allocations
 *             properties:
 *               orderId:
 *                 type: integer
 *                 description: 订单ID
 *               allocations:
 *                 type: array
 *                 minItems: 1
 *                 description: 设备分配列表
 *                 items:
 *                   type: object
 *                   required:
 *                     - deviceId
 *                     - patternId
 *                     - lathesNum
 *                   properties:
 *                     deviceId:
 *                       type: integer
 *                       description: 设备ID
 *                     patternId:
 *                       type: integer
 *                       description: 花样ID
 *                     lathesNum:
 *                       type: integer
 *                       minimum: 1
 *                       description: 车数
 *                     formula:
 *                       type: string
 *                       description: 计算公式
 *                       example: "1件=1头"
 *     responses:
 *       201:
 *         description: 批量创建排产记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 批量创建排产记录成功
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DeviceOrderSequence'
 */
router.post('/batch',
  authMiddleware,
  permissionMiddleware(['production:create']),
  deviceOrderSequenceValidationRules.batchCreateSequences,
  asyncHandler(deviceOrderSequenceController.batchCreateSequences)
);

/**
 * @swagger
 * /api/v1/sequences/{id}:
 *   put:
 *     summary: 更新排产记录
 *     tags: [设备排产]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 排产记录ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               formula:
 *                 type: string
 *                 description: 计算公式
 *                 example: "1件=1头"
 *               lathesNum:
 *                 type: integer
 *                 description: 车数
 *               productionSequence:
 *                 type: integer
 *                 description: 生产排序
 *               status:
 *                 type: integer
 *                 enum: [0, 1, 2]
 *                 description: 状态
 *     responses:
 *       200:
 *         description: 更新排产记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新排产记录成功
 *                 data:
 *                   $ref: '#/components/schemas/DeviceOrderSequence'
 */
router.put('/:id', 
  authMiddleware, 
  permissionMiddleware(['production:update']), 
  deviceOrderSequenceValidationRules.updateSequence, 
  asyncHandler(deviceOrderSequenceController.updateSequence)
);

/**
 * @swagger
 * /api/v1/sequences/{id}:
 *   delete:
 *     summary: 删除排产记录
 *     tags: [设备排产]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 排产记录ID
 *     responses:
 *       200:
 *         description: 删除排产记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除排产记录成功
 */
router.delete('/:id',
  authMiddleware,
  permissionMiddleware(['production:delete']),
  deviceOrderSequenceValidationRules.deleteSequence,
  asyncHandler(deviceOrderSequenceController.deleteSequence)
);

/**
 * @swagger
 * /api/v1/sequences/order/{orderId}:
 *   get:
 *     summary: 根据订单获取排产记录
 *     tags: [设备排产]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 获取订单排产记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取订单排产记录成功
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DeviceOrderSequence'
 */
router.get('/order/:orderId',
  authMiddleware,
  permissionMiddleware(['production:view']),
  deviceOrderSequenceValidationRules.getSequencesByOrder,
  asyncHandler(deviceOrderSequenceController.getSequencesByOrder)
);

/**
 * @swagger
 * /api/v1/sequences/device/{deviceId}:
 *   get:
 *     summary: 根据设备获取排产队列
 *     tags: [设备排产]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: deviceId
 *         required: true
 *         schema:
 *           type: integer
 *         description: 设备ID
 *     responses:
 *       200:
 *         description: 获取设备排产队列成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取设备排产队列成功
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/DeviceOrderSequence'
 */
router.get('/device/:deviceId',
  authMiddleware,
  permissionMiddleware(['production:view']),
  deviceOrderSequenceValidationRules.getSequencesByDevice,
  asyncHandler(deviceOrderSequenceController.getSequencesByDevice)
);

/**
 * @swagger
 * /api/v1/sequences/reorder:
 *   put:
 *     summary: 重新排序排产记录
 *     tags: [设备排产]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - deviceId
 *               - sequenceIds
 *             properties:
 *               deviceId:
 *                 type: integer
 *                 description: 设备ID
 *               sequenceIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: 排产记录ID列表（按新的排序）
 *     responses:
 *       200:
 *         description: 重新排序排产记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 重新排序排产记录成功
 */
router.put('/reorder',
  authMiddleware,
  permissionMiddleware(['production:update']),
  deviceOrderSequenceValidationRules.reorderSequences,
  asyncHandler(deviceOrderSequenceController.reorderSequences)
);

/**
 * @swagger
 * /api/v1/sequences/calculate:
 *   post:
 *     summary: 计算生产数量
 *     tags: [设备排产]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - lathesNum
 *               - deviceId
 *               - formula
 *             properties:
 *               lathesNum:
 *                 type: integer
 *                 description: 车数
 *               deviceId:
 *                 type: integer
 *                 description: 设备ID
 *               formula:
 *                 type: string
 *                 description: 计算公式
 *                 example: "1件=1头"
 *     responses:
 *       200:
 *         description: 计算生产数量成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 计算生产数量成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     productionQuantity:
 *                       type: integer
 *                       description: 生产数量
 *                     formula:
 *                       type: string
 *                       description: 计算公式
 *                     lathesNum:
 *                       type: integer
 *                       description: 车数
 *                     deviceHeadNum:
 *                       type: integer
 *                       description: 设备头数
 */
router.post('/calculate',
  authMiddleware,
  permissionMiddleware(['production:view']),
  deviceOrderSequenceValidationRules.calculateProductionQuantity,
  asyncHandler(deviceOrderSequenceController.calculateProductionQuantity)
);

/**
 * @swagger
 * /api/v1/sequences/{id}/start:
 *   put:
 *     summary: 开始生产
 *     tags: [生产管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 排产记录ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               remark:
 *                 type: string
 *                 description: 备注说明
 *     responses:
 *       200:
 *         description: 开始生产成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 开始生产成功
 *                 data:
 *                   $ref: '#/components/schemas/DeviceOrderSequence'
 */
router.put('/:id/start',
  authMiddleware,
  permissionMiddleware(['production:update']),
  deviceOrderSequenceValidationRules.startProduction,
  asyncHandler(deviceOrderSequenceController.startProduction)
);

/**
 * @swagger
 * /api/v1/sequences/{id}/complete:
 *   put:
 *     summary: 完成生产
 *     tags: [生产管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 排产记录ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               actualQuantity:
 *                 type: integer
 *                 description: 实际生产数量
 *               remark:
 *                 type: string
 *                 description: 备注说明
 *     responses:
 *       200:
 *         description: 完成生产成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 完成生产成功
 *                 data:
 *                   $ref: '#/components/schemas/DeviceOrderSequence'
 */
router.put('/:id/complete',
  authMiddleware,
  permissionMiddleware(['production:update']),
  deviceOrderSequenceValidationRules.completeProduction,
  asyncHandler(deviceOrderSequenceController.completeProduction)
);

/**
 * @swagger
 * /api/v1/sequences/{id}/progress:
 *   put:
 *     summary: 更新生产进度
 *     tags: [生产管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 排产记录ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - progress
 *             properties:
 *               progress:
 *                 type: integer
 *                 minimum: 0
 *                 maximum: 100
 *                 description: 生产进度百分比
 *               remark:
 *                 type: string
 *                 description: 备注说明
 *     responses:
 *       200:
 *         description: 更新生产进度成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新生产进度成功
 *                 data:
 *                   $ref: '#/components/schemas/DeviceOrderSequence'
 */
router.put('/:id/progress',
  authMiddleware,
  permissionMiddleware(['production:update']),
  deviceOrderSequenceValidationRules.updateProgress,
  asyncHandler(deviceOrderSequenceController.updateProgress)
);

/**
 * @swagger
 * /api/v1/sequences/statistics:
 *   get:
 *     summary: 获取生产统计数据
 *     tags: [生产管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取生产统计数据成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取生产统计数据成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalSequences:
 *                       type: integer
 *                       description: 总排产数量
 *                     completedSequences:
 *                       type: integer
 *                       description: 已完成数量
 *                     inProgressSequences:
 *                       type: integer
 *                       description: 生产中数量
 *                     waitingSequences:
 *                       type: integer
 *                       description: 等待中数量
 *                     completionRate:
 *                       type: number
 *                       description: 完成率
 *                     averageProductionTime:
 *                       type: number
 *                       description: 平均生产时间（分钟）
 *                     deviceUtilization:
 *                       type: array
 *                       description: 设备利用率统计
 *                       items:
 *                         type: object
 *                         properties:
 *                           deviceId:
 *                             type: integer
 *                           deviceName:
 *                             type: string
 *                           utilizationRate:
 *                             type: number
 *                           totalSequences:
 *                             type: integer
 *                           completedSequences:
 *                             type: integer
 *                     dailyStats:
 *                       type: array
 *                       description: 每日统计
 *                       items:
 *                         type: object
 *                         properties:
 *                           date:
 *                             type: string
 *                           completedCount:
 *                             type: integer
 *                           averageTime:
 *                             type: number
 */
router.get('/statistics',
  authMiddleware,
  permissionMiddleware(['production:view']),
  deviceOrderSequenceValidationRules.getProductionStatistics,
  asyncHandler(deviceOrderSequenceController.getProductionStatistics)
);

/**
 * @swagger
 * /api/v1/sequences/{id}/history:
 *   get:
 *     summary: 获取生产历史记录
 *     tags: [生产管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 排产记录ID
 *       - in: query
 *         name: actionType
 *         schema:
 *           type: string
 *           enum: [START, PAUSE, RESUME, COMPLETE, CANCEL, ERROR, PROGRESS_UPDATE, QUANTITY_UPDATE]
 *         description: 操作类型
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取生产历史记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取生产历史记录成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     histories:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           sequenceId:
 *                             type: integer
 *                           actionType:
 *                             type: string
 *                           actionTime:
 *                             type: string
 *                             format: date-time
 *                           operatorId:
 *                             type: integer
 *                           previousValue:
 *                             type: string
 *                           newValue:
 *                             type: string
 *                           remark:
 *                             type: string
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 */
router.get('/:id/history',
  authMiddleware,
  permissionMiddleware(['production:view']),
  deviceOrderSequenceValidationRules.getProductionHistory,
  asyncHandler(deviceOrderSequenceController.getProductionHistory)
);

/**
 * @swagger
 * /api/v1/sequences/history:
 *   get:
 *     summary: 获取所有生产历史记录
 *     tags: [生产管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: actionType
 *         schema:
 *           type: string
 *           enum: [START, PAUSE, RESUME, COMPLETE, CANCEL, ERROR, PROGRESS_UPDATE, QUANTITY_UPDATE]
 *         description: 操作类型
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取生产历史记录成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取生产历史记录成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     histories:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           sequenceId:
 *                             type: integer
 *                           actionType:
 *                             type: string
 *                           actionTime:
 *                             type: string
 *                             format: date-time
 *                           operatorId:
 *                             type: integer
 *                           previousValue:
 *                             type: string
 *                           newValue:
 *                             type: string
 *                           remark:
 *                             type: string
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 */
router.get('/history',
  authMiddleware,
  permissionMiddleware(['production:view']),
  deviceOrderSequenceValidationRules.getProductionHistory,
  asyncHandler(deviceOrderSequenceController.getProductionHistory)
);

export default router;
