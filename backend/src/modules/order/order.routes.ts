/**
 * 订单路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 18:15:00 +08:00; Reason: Shrimp Task ID: #64d308e1-c835-4f08-97a8-7f99f430939a, 创建订单路由配置; Principle_Applied: 路由分离;}}
 */

import { Router } from 'express';
import { OrderController, orderValidationRules } from './order.controller';
import { authMiddleware, permissionMiddleware } from '../../shared/middleware/auth.middleware';
import { asyncHandler } from '../../shared/middleware/async.middleware';

const router = Router();
const orderController = new OrderController();

/**
 * @swagger
 * components:
 *   schemas:
 *     Order:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 订单ID
 *         enterpriseId:
 *           type: integer
 *           description: 企业ID
 *         type:
 *           type: integer
 *           description: 订单类型
 *           enum: [1, 2, 3, 4]
 *         status:
 *           type: integer
 *           description: 订单状态
 *           enum: [0, 1, 2, 3]
 *         code:
 *           type: string
 *           description: 订单编号
 *         customerName:
 *           type: string
 *           description: 客户名称
 *         salesman:
 *           type: string
 *           description: 业务员
 *         orderDate:
 *           type: string
 *           format: date-time
 *           description: 下单日期
 *         deliveryDate:
 *           type: string
 *           format: date-time
 *           description: 交货日期
 *         orderUnitId:
 *           type: integer
 *           description: 记量单位ID
 *         orderQuantity:
 *           type: integer
 *           description: 订单量
 *         remark:
 *           type: string
 *           description: 订单备注
 *         orderFiles:
 *           type: array
 *           items:
 *             type: string
 *           description: 订单文件列表
 *         patternInfo:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               patternId:
 *                 type: string
 *                 description: 花样ID
 *               patternQuantity:
 *                 type: integer
 *                 description: 花样数量
 *           description: 花样信息
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 */

/**
 * @swagger
 * /api/v1/orders:
 *   get:
 *     summary: 获取订单列表
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1, 2, 3]
 *         description: 订单状态
 *       - in: query
 *         name: type
 *         schema:
 *           type: integer
 *           enum: [1, 2, 3, 4]
 *         description: 订单类型
 *       - in: query
 *         name: customerName
 *         schema:
 *           type: string
 *         description: 客户名称
 *       - in: query
 *         name: salesman
 *         schema:
 *           type: string
 *         description: 业务员
 *       - in: query
 *         name: orderDateStart
 *         schema:
 *           type: string
 *           format: date
 *         description: 下单开始日期
 *       - in: query
 *         name: orderDateEnd
 *         schema:
 *           type: string
 *           format: date
 *         description: 下单结束日期
 *       - in: query
 *         name: deliveryDateStart
 *         schema:
 *           type: string
 *           format: date
 *         description: 交货开始日期
 *       - in: query
 *         name: deliveryDateEnd
 *         schema:
 *           type: string
 *           format: date
 *         description: 交货结束日期
 *     responses:
 *       200:
 *         description: 获取订单列表成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取订单列表成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     orders:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Order'
 *                     total:
 *                       type: integer
 *                     page:
 *                       type: integer
 *                     pageSize:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 */
router.get('/', 
  authMiddleware, 
  permissionMiddleware(['order:view']), 
  orderValidationRules.getOrderList, 
  asyncHandler(orderController.getOrderList)
);

/**
 * @swagger
 * /api/v1/orders/search-options:
 *   get:
 *     summary: 获取订单搜索选项
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取搜索选项成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取搜索选项成功
 *                 data:
 *                   type: object
 *                   properties:
 *                     customers:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     salesmen:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     patterns:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           code:
 *                             type: string
 *                     devices:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           name:
 *                             type: string
 *                           code:
 *                             type: string
 */
router.get('/search-options',
  authMiddleware,
  permissionMiddleware(['order:view']),
  asyncHandler(orderController.getSearchOptions)
);

/**
 * @swagger
 * /api/v1/orders/{id}:
 *   get:
 *     summary: 获取订单详情
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 获取订单详情成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 获取订单详情成功
 *                 data:
 *                   $ref: '#/components/schemas/Order'
 *       404:
 *         description: 订单不存在
 */
router.get('/:id',
  authMiddleware,
  permissionMiddleware(['order:view']),
  orderValidationRules.getOrderById,
  asyncHandler(orderController.getOrderById)
);

/**
 * @swagger
 * /api/v1/orders:
 *   post:
 *     summary: 创建订单
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: integer
 *                 enum: [1, 2, 3, 4]
 *                 description: 订单类型
 *               code:
 *                 type: string
 *                 description: 订单编号
 *               customerName:
 *                 type: string
 *                 description: 客户名称
 *               salesman:
 *                 type: string
 *                 description: 业务员
 *               orderDate:
 *                 type: string
 *                 format: date-time
 *                 description: 下单日期
 *               deliveryDate:
 *                 type: string
 *                 format: date-time
 *                 description: 交货日期
 *               orderUnitId:
 *                 type: integer
 *                 description: 记量单位ID
 *               orderQuantity:
 *                 type: integer
 *                 description: 订单量
 *               remark:
 *                 type: string
 *                 description: 订单备注
 *               orderFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 订单文件列表
 *               patternInfo:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     patternId:
 *                       type: string
 *                       description: 花样ID
 *                     patternQuantity:
 *                       type: integer
 *                       description: 花样数量
 *                 description: 花样信息
 *     responses:
 *       201:
 *         description: 创建订单成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 201
 *                 message:
 *                   type: string
 *                   example: 创建订单成功
 *                 data:
 *                   $ref: '#/components/schemas/Order'
 */
router.post('/',
  authMiddleware,
  permissionMiddleware(['order:create']),
  orderValidationRules.createOrder,
  asyncHandler(orderController.createOrder)
);

/**
 * @swagger
 * /api/v1/orders/{id}:
 *   put:
 *     summary: 更新订单
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: integer
 *                 enum: [1, 2, 3, 4]
 *                 description: 订单类型
 *               code:
 *                 type: string
 *                 description: 订单编号
 *               customerName:
 *                 type: string
 *                 description: 客户名称
 *               salesman:
 *                 type: string
 *                 description: 业务员
 *               orderDate:
 *                 type: string
 *                 format: date-time
 *                 description: 下单日期
 *               deliveryDate:
 *                 type: string
 *                 format: date-time
 *                 description: 交货日期
 *               orderUnitId:
 *                 type: integer
 *                 description: 记量单位ID
 *               orderQuantity:
 *                 type: integer
 *                 description: 订单量
 *               remark:
 *                 type: string
 *                 description: 订单备注
 *               orderFiles:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 订单文件列表
 *               patternInfo:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     patternId:
 *                       type: string
 *                       description: 花样ID
 *                     patternQuantity:
 *                       type: integer
 *                       description: 花样数量
 *                 description: 花样信息
 *     responses:
 *       200:
 *         description: 更新订单成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新订单成功
 *                 data:
 *                   $ref: '#/components/schemas/Order'
 */
router.put('/:id',
  authMiddleware,
  permissionMiddleware(['order:update']),
  orderValidationRules.updateOrder,
  asyncHandler(orderController.updateOrder)
);

/**
 * @swagger
 * /api/v1/orders/{id}:
 *   delete:
 *     summary: 删除订单
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     responses:
 *       200:
 *         description: 删除订单成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 删除订单成功
 */
router.delete('/:id',
  authMiddleware,
  permissionMiddleware(['order:delete']),
  orderValidationRules.deleteOrder,
  asyncHandler(orderController.deleteOrder)
);

/**
 * @swagger
 * /api/v1/orders/{id}/status:
 *   put:
 *     summary: 更新订单状态
 *     tags: [订单管理]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 订单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: integer
 *                 enum: [0, 1, 2, 3]
 *                 description: 订单状态
 *               remark:
 *                 type: string
 *                 description: 备注
 *     responses:
 *       200:
 *         description: 更新订单状态成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: 更新订单状态成功
 *                 data:
 *                   $ref: '#/components/schemas/Order'
 */
router.put('/:id/status',
  authMiddleware,
  permissionMiddleware(['order:update']),
  orderValidationRules.updateOrderStatus,
  asyncHandler(orderController.updateOrderStatus)
);

export default router;
