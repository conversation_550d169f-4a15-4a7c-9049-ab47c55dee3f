/**
 * 设备当前生产状态服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-30 17:45:00 +08:00; Reason: 用户需求，创建设备当前生产状态服务; Principle_Applied: 服务层设计;}}
 */

import { Transaction } from 'sequelize';
import { 
  DeviceCurrentProduction, 
  DeviceCurrentProductionAttributes, 
  DeviceCurrentProductionCreationAttributes,
  Device,
  Order,
  DeviceOrderSequence,
  Pattern,
  Enterprise
} from '../../shared/database/models';
import { createApiError } from '../../shared/middleware/error.middleware';

// 设备当前生产状态查询参数
export interface DeviceCurrentProductionListQuery {
  page?: number;
  pageSize?: number;
  deviceId?: number;
  orderId?: number;
  enterpriseId: number;
}

// 设备当前生产状态列表响应
export interface DeviceCurrentProductionListResponse {
  list: DeviceCurrentProductionAttributes[];
  total: number;
  page: number;
  pageSize: number;
}

// 设备当前生产状态服务类
export class DeviceCurrentProductionService {
  /**
   * 获取设备当前生产状态列表
   */
  public static async getDeviceCurrentProductionList(
    query: DeviceCurrentProductionListQuery
  ): Promise<DeviceCurrentProductionListResponse> {
    try {
      const { page = 1, pageSize = 10, deviceId, orderId, enterpriseId } = query;
      const offset = (page - 1) * pageSize;

      // 构建查询条件
      const whereConditions: any = {
        enterpriseId,
      };

      if (deviceId !== undefined) {
        whereConditions.deviceId = deviceId;
      }

      if (orderId !== undefined) {
        whereConditions.orderId = orderId;
      }

      // 查询数据
      const { rows: list, count: total } = await DeviceCurrentProduction.findAndCountAll({
        where: whereConditions,
        include: [
          {
            model: Device,
            as: 'device',
            attributes: ['id', 'name', 'code', 'status'],
          },
          {
            model: Order,
            as: 'order',
            attributes: ['id', 'orderCode', 'customerName', 'orderQuantity'],
          },
          {
            model: DeviceOrderSequence,
            as: 'sequence',
            attributes: ['id', 'productionQuantity', 'actualQuantity', 'status'],
          },
          {
            model: Pattern,
            as: 'pattern',
            attributes: ['id', 'name', 'code'],
          },
        ],
        order: [['startTime', 'DESC']],
        limit: pageSize,
        offset,
      });

      return {
        list,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('获取设备当前生产状态列表失败:', error);
      throw createApiError('获取设备当前生产状态列表失败', 500);
    }
  }

  /**
   * 根据设备ID获取当前生产状态
   */
  public static async getCurrentProductionByDeviceId(
    deviceId: number,
    enterpriseId: number
  ): Promise<DeviceCurrentProductionAttributes[]> {
    try {
      const productions = await DeviceCurrentProduction.findAll({
        where: {
          deviceId,
          enterpriseId,
        },
        include: [
          {
            model: Order,
            as: 'order',
            attributes: ['id', 'orderCode', 'customerName', 'orderQuantity'],
          },
          {
            model: DeviceOrderSequence,
            as: 'sequence',
            attributes: ['id', 'productionQuantity', 'actualQuantity', 'status'],
          },
          {
            model: Pattern,
            as: 'pattern',
            attributes: ['id', 'name', 'code'],
          },
        ],
        order: [['startTime', 'ASC']],
      });

      return productions;
    } catch (error) {
      console.error('获取设备当前生产状态失败:', error);
      throw createApiError('获取设备当前生产状态失败', 500);
    }
  }

  /**
   * 开始生产 - 创建当前生产记录
   */
  public static async startProduction(
    data: DeviceCurrentProductionCreationAttributes,
    transaction?: Transaction
  ): Promise<DeviceCurrentProductionAttributes> {
    try {
      // 验证排产记录是否存在且状态正确
      const sequence = await DeviceOrderSequence.findByPk(data.sequenceId, { transaction });
      if (!sequence) {
        throw createApiError('排产记录不存在', 404);
      }

      // 检查是否已经有当前生产记录
      const existingProduction = await DeviceCurrentProduction.findOne({
        where: { sequenceId: data.sequenceId },
        transaction,
      });

      if (existingProduction) {
        throw createApiError('该排产记录已在生产中', 400);
      }

      // 获取订单信息
      const order = await Order.findByPk(data.orderId, { transaction });
      if (!order) {
        throw createApiError('订单不存在', 404);
      }

      // 创建当前生产记录
      const productionData: DeviceCurrentProductionCreationAttributes = {
        ...data,
        orderCode: order.orderCode,
        startTime: data.startTime || new Date(),
        progress: data.progress || 0,
      };

      const production = await DeviceCurrentProduction.create(productionData, { transaction });

      return production;
    } catch (error) {
      console.error('开始生产失败:', error);
      if (error.message) {
        throw error;
      }
      throw createApiError('开始生产失败', 500);
    }
  }

  /**
   * 完成生产 - 删除当前生产记录
   */
  public static async completeProduction(
    sequenceId: number,
    enterpriseId: number,
    transaction?: Transaction
  ): Promise<void> {
    try {
      const result = await DeviceCurrentProduction.destroy({
        where: {
          sequenceId,
          enterpriseId,
        },
        transaction,
      });

      if (result === 0) {
        throw createApiError('当前生产记录不存在', 404);
      }
    } catch (error) {
      console.error('完成生产失败:', error);
      if (error.message) {
        throw error;
      }
      throw createApiError('完成生产失败', 500);
    }
  }

  /**
   * 更新生产进度
   */
  public static async updateProgress(
    sequenceId: number,
    progress: number,
    enterpriseId: number,
    transaction?: Transaction
  ): Promise<DeviceCurrentProductionAttributes> {
    try {
      const production = await DeviceCurrentProduction.findOne({
        where: {
          sequenceId,
          enterpriseId,
        },
        transaction,
      });

      if (!production) {
        throw createApiError('当前生产记录不存在', 404);
      }

      await production.updateProgress(progress);

      return production;
    } catch (error) {
      console.error('更新生产进度失败:', error);
      if (error.message) {
        throw error;
      }
      throw createApiError('更新生产进度失败', 500);
    }
  }

  /**
   * 获取企业所有设备的当前生产状态概览
   */
  public static async getProductionOverview(
    enterpriseId: number
  ): Promise<any> {
    try {
      const productions = await DeviceCurrentProduction.findAll({
        where: { enterpriseId },
        include: [
          {
            model: Device,
            as: 'device',
            attributes: ['id', 'name', 'code', 'status'],
          },
          {
            model: Order,
            as: 'order',
            attributes: ['id', 'orderCode', 'customerName'],
          },
        ],
        order: [['startTime', 'ASC']],
      });

      // 按设备分组
      const deviceProductions = productions.reduce((acc, production) => {
        const deviceId = production.deviceId;
        if (!acc[deviceId]) {
          acc[deviceId] = {
            device: production.device,
            productions: [],
          };
        }
        acc[deviceId].productions.push(production);
        return acc;
      }, {} as any);

      return Object.values(deviceProductions);
    } catch (error) {
      console.error('获取生产概览失败:', error);
      throw createApiError('获取生产概览失败', 500);
    }
  }
}
