/**
 * 设备当前生产状态服务
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-30 17:45:00 +08:00; Reason: 用户需求，创建设备当前生产状态服务; Principle_Applied: 服务层设计;}}
 */

import { Transaction } from 'sequelize';
import { 
  DeviceCurrentProduction, 
  DeviceCurrentProductionAttributes, 
  DeviceCurrentProductionCreationAttributes,
  Device,
  Order,
  DeviceOrderSequence,
  Pattern,
  Enterprise
} from '../../shared/database/models';
import { createApiError } from '../../shared/middleware/error.middleware';

// 设备当前生产状态查询参数
export interface DeviceCurrentProductionListQuery {
  page?: number;
  pageSize?: number;
  deviceSn?: string;
  orderCode?: string;
  enterpriseId: number;
}

// 设备当前生产状态列表响应
export interface DeviceCurrentProductionListResponse {
  list: DeviceCurrentProductionAttributes[];
  total: number;
  page: number;
  pageSize: number;
}

// 设备当前生产状态服务类
export class DeviceCurrentProductionService {
  /**
   * 获取设备当前生产状态列表
   */
  public static async getDeviceCurrentProductionList(
    query: DeviceCurrentProductionListQuery
  ): Promise<DeviceCurrentProductionListResponse> {
    try {
      const { page = 1, pageSize = 10, deviceSn, orderCode, enterpriseId } = query;
      const offset = (page - 1) * pageSize;

      // 构建查询条件
      const whereConditions: any = {
        enterpriseId,
      };

      if (deviceSn) {
        whereConditions.deviceSn = deviceSn;
      }

      if (orderCode) {
        whereConditions.orderCode = orderCode;
      }

      // 查询数据
      const { rows: list, count: total } = await DeviceCurrentProduction.findAndCountAll({
        where: whereConditions,
        // 暂时不包含关联数据，避免启动时的循环引用问题
        // include: [
        //   {
        //     model: Device,
        //     as: 'device',
        //     attributes: ['id', 'name', 'code', 'sn', 'status'],
        //   },
        //   {
        //     model: Order,
        //     as: 'order',
        //     attributes: ['id', 'customerName', 'orderQuantity'],
        //   },
        //   {
        //     model: DeviceOrderSequence,
        //     as: 'sequence',
        //     attributes: ['id', 'productionQuantity', 'actualQuantity', 'status'],
        //   },
        //   {
        //     model: Pattern,
        //     as: 'pattern',
        //     attributes: ['id', 'name'],
        //   },
        // ],
        order: [['createdAt', 'DESC']],
        limit: pageSize,
        offset,
      });

      return {
        list,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      console.error('获取设备当前生产状态列表失败:', error);
      throw createApiError('获取设备当前生产状态列表失败', 500);
    }
  }

  /**
   * 根据设备SN获取当前生产状态
   */
  public static async getCurrentProductionByDeviceSn(
    deviceSn: string,
    enterpriseId: number
  ): Promise<DeviceCurrentProductionAttributes | null> {
    try {
      const production = await DeviceCurrentProduction.findOne({
        where: {
          deviceSn,
          enterpriseId,
        },
        // 暂时不包含关联数据，避免启动时的循环引用问题
        // include: [
        //   {
        //     model: Device,
        //     as: 'device',
        //     attributes: ['id', 'name', 'code', 'sn', 'status'],
        //   },
        //   {
        //     model: Order,
        //     as: 'order',
        //     attributes: ['id', 'customerName', 'orderQuantity'],
        //   },
        //   {
        //     model: DeviceOrderSequence,
        //     as: 'sequence',
        //     attributes: ['id', 'productionQuantity', 'actualQuantity', 'status'],
        //   },
        //   {
        //     model: Pattern,
        //     as: 'pattern',
        //     attributes: ['id', 'name'],
        //   },
        // ],
      });

      return production;
    } catch (error) {
      console.error('根据设备SN获取当前生产状态失败:', error);
      throw createApiError('根据设备SN获取当前生产状态失败', 500);
    }
  }

  /**
   * 开始生产 - 创建当前生产记录
   */
  public static async startProduction(
    data: DeviceCurrentProductionCreationAttributes,
    transaction?: Transaction
  ): Promise<DeviceCurrentProductionAttributes> {
    try {
      // 检查设备是否已经在生产中
      const existingProduction = await DeviceCurrentProduction.findOne({
        where: {
          deviceSn: data.deviceSn,
          enterpriseId: data.enterpriseId
        },
        transaction,
      });

      if (existingProduction) {
        throw createApiError('该设备已在生产中，请先完成当前生产', 400);
      }

      // 创建当前生产记录
      const production = await DeviceCurrentProduction.create(data, { transaction });

      return production;
    } catch (error) {
      console.error('开始生产失败:', error);
      if (error.message) {
        throw error;
      }
      throw createApiError('开始生产失败', 500);
    }
  }

  /**
   * 完成生产 - 删除当前生产记录
   */
  public static async completeProduction(
    deviceSn: string,
    enterpriseId: number,
    transaction?: Transaction
  ): Promise<void> {
    try {
      const result = await DeviceCurrentProduction.destroy({
        where: {
          deviceSn,
          enterpriseId,
        },
        transaction,
      });

      if (result === 0) {
        throw createApiError('该设备当前没有生产记录', 404);
      }
    } catch (error) {
      console.error('完成生产失败:', error);
      if (error.message) {
        throw error;
      }
      throw createApiError('完成生产失败', 500);
    }
  }



  /**
   * 获取企业所有设备的当前生产状态概览
   */
  public static async getProductionOverview(
    enterpriseId: number
  ): Promise<any> {
    try {
      const productions = await DeviceCurrentProduction.findAll({
        where: { enterpriseId },
        include: [
          {
            model: Device,
            as: 'device',
            attributes: ['id', 'name', 'code', 'status'],
          },
          {
            model: Order,
            as: 'order',
            attributes: ['id', 'orderCode', 'customerName'],
          },
        ],
        order: [['startTime', 'ASC']],
      });

      // 按设备分组
      const deviceProductions = productions.reduce((acc, production) => {
        const deviceId = production.deviceId;
        if (!acc[deviceId]) {
          acc[deviceId] = {
            device: production.device,
            productions: [],
          };
        }
        acc[deviceId].productions.push(production);
        return acc;
      }, {} as any);

      return Object.values(deviceProductions);
    } catch (error) {
      console.error('获取生产概览失败:', error);
      throw createApiError('获取生产概览失败', 500);
    }
  }
}
