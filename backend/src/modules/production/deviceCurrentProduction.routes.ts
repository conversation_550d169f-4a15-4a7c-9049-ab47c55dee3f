/**
 * 设备当前生产状态路由
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-30 17:45:00 +08:00; Reason: 用户需求，创建设备当前生产状态路由; Principle_Applied: 路由设计;}}
 */

import { Router } from 'express';
import { DeviceCurrentProductionController, deviceCurrentProductionValidationRules } from './deviceCurrentProduction.controller';
import { authMiddleware } from '../../shared/middleware/auth.middleware';
import { permissionMiddleware } from '../../shared/middleware/permission.middleware';

const router = Router();

// 应用认证中间件
router.use(authMiddleware);

/**
 * @route GET /api/v1/device-current-production
 * @desc 获取设备当前生产状态列表
 * @access Private (需要生产计划查看权限)
 */
router.get(
  '/',
  permissionMiddleware(['production_plan:view']),
  deviceCurrentProductionValidationRules.getDeviceCurrentProductionList,
  DeviceCurrentProductionController.getDeviceCurrentProductionList
);

/**
 * @route GET /api/v1/device-current-production/device/:deviceSn
 * @desc 根据设备SN获取当前生产状态
 * @access Private (需要生产计划查看权限)
 */
router.get(
  '/device/:deviceSn',
  permissionMiddleware(['production_plan:view']),
  DeviceCurrentProductionController.getCurrentProductionByDeviceSn
);



/**
 * @route GET /api/v1/device-current-production/overview
 * @desc 获取企业生产概览
 * @access Private (需要生产计划查看权限)
 */
router.get(
  '/overview',
  permissionMiddleware(['production_plan:view']),
  DeviceCurrentProductionController.getProductionOverview
);

export default router;
