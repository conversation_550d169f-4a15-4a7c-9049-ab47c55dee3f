/**
 * 设备当前生产状态控制器
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-30 17:45:00 +08:00; Reason: 用户需求，创建设备当前生产状态控制器; Principle_Applied: 控制器层设计;}}
 */

import { Request, Response } from 'express';
import { query, validationResult } from 'express-validator';
import { DeviceCurrentProductionService, DeviceCurrentProductionListQuery } from './deviceCurrentProduction.service';
import { createApiError } from '../../shared/middleware/error.middleware';
import { asyncHandler } from '../../shared/middleware/async.middleware';

// 设备当前生产状态控制器类
export class DeviceCurrentProductionController {
  /**
   * 获取设备当前生产状态列表
   */
  public static getDeviceCurrentProductionList = asyncHandler(async (req: Request, res: Response) => {
    // 参数验证
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw createApiError('参数验证失败', 400);
    }

    try {
      const query: DeviceCurrentProductionListQuery = {
        page: parseInt(req.query.page as string) || 1,
        pageSize: parseInt(req.query.pageSize as string) || 10,
        deviceSn: req.query.deviceSn as string,
        orderCode: req.query.orderCode as string,
        enterpriseId: req.user!.enterpriseId
      };

      const result = await DeviceCurrentProductionService.getDeviceCurrentProductionList(query);

      res.json({
        code: 200,
        message: '获取设备当前生产状态列表成功',
        data: result,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('获取设备当前生产状态列表失败:', error);
      throw error;
    }
  });

  /**
   * 根据设备SN获取当前生产状态
   */
  public static getCurrentProductionByDeviceSn = asyncHandler(async (req: Request, res: Response) => {
    try {
      const deviceSn = req.params.deviceSn;
      if (!deviceSn) {
        throw createApiError('设备SN不能为空', 400);
      }

      const production = await DeviceCurrentProductionService.getCurrentProductionByDeviceSn(
        deviceSn,
        req.user!.enterpriseId
      );

      res.json({
        code: 200,
        message: '获取设备当前生产状态成功',
        data: production,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('获取设备当前生产状态失败:', error);
      throw error;
    }
  });



  /**
   * 获取企业生产概览
   */
  public static getProductionOverview = asyncHandler(async (req: Request, res: Response) => {
    try {
      const overview = await DeviceCurrentProductionService.getProductionOverview(
        req.user!.enterpriseId
      );

      res.json({
        code: 200,
        message: '获取生产概览成功',
        data: overview,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('获取生产概览失败:', error);
      throw error;
    }
  });
}

// 验证规则
export const deviceCurrentProductionValidationRules = {
  // 获取设备当前生产状态列表验证规则
  getDeviceCurrentProductionList: [
    query('page').optional().isInt({ min: 1 }).withMessage('页码必须是大于0的整数'),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须是1-100之间的整数'),
    query('deviceSn').optional().isString().withMessage('设备SN必须是字符串'),
    query('orderCode').optional().isString().withMessage('订单编号必须是字符串'),
  ],
};
