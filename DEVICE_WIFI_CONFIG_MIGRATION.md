# 设备WiFi配置字段重构迁移指南

## 重构概述

将设备表中的多个WiFi相关字段合并为一个JSON字段，提高数据结构的灵活性和可维护性。

### 原有字段 → 新字段
```
wifi_bit_rate      ↘
wifi_freq          ↘
wifi_ip            ↘
wifi_key_mgmt      ↘
wifi_mac           ↘  wifi_config (JSON)
wifi_ssid          ↘
wifi_state         ↘
wifi_link_quality  ↘
wifi_signal_level  ↘
gateway_mac        ↘
```

## 数据结构变更

### 新的WiFi配置接口
```typescript
export interface WifiConfig {
  bitRate?: number;        // WiFi速率
  freq?: number;           // 无线频率
  ip?: string;             // WiFi的IP
  keyMgmt?: string;        // WiFi加密方式
  mac?: string;            // WiFi的mac
  ssid?: string;           // WiFi名称
  state?: string;          // WiFi连接状态
  linkQuality?: string;    // WiFi信号质量
  signalLevel?: string;    // WiFi信号强度
  gatewayMac?: string;     // 网关Mac
}
```

### JSON字段示例
```json
{
  "bitRate": 54.0,
  "freq": 2.4,
  "ip": "*************",
  "keyMgmt": "WPA2-PSK",
  "mac": "00:11:22:33:44:55",
  "ssid": "Factory_WiFi",
  "state": "connected",
  "linkQuality": "70/70",
  "signalLevel": "-40 dBm",
  "gatewayMac": "00:11:22:33:44:66"
}
```

## 数据库迁移步骤

### 1. 添加新字段
```sql
-- 添加新的wifi_config字段
ALTER TABLE em_device ADD COLUMN wifi_config TEXT COMMENT 'WiFi配置信息(JSON格式)';
```

### 2. 数据迁移脚本
```sql
-- 将现有WiFi字段数据迁移到JSON格式
UPDATE em_device 
SET wifi_config = JSON_OBJECT(
  'bitRate', wifi_bit_rate,
  'freq', wifi_freq,
  'ip', wifi_ip,
  'keyMgmt', wifi_key_mgmt,
  'mac', wifi_mac,
  'ssid', wifi_ssid,
  'state', wifi_state,
  'linkQuality', wifi_link_quality,
  'signalLevel', wifi_signal_level,
  'gatewayMac', gateway_mac
)
WHERE wifi_bit_rate IS NOT NULL 
   OR wifi_freq IS NOT NULL 
   OR wifi_ip IS NOT NULL 
   OR wifi_key_mgmt IS NOT NULL 
   OR wifi_mac IS NOT NULL 
   OR wifi_ssid IS NOT NULL 
   OR wifi_state IS NOT NULL 
   OR wifi_link_quality IS NOT NULL 
   OR wifi_signal_level IS NOT NULL 
   OR gateway_mac IS NOT NULL;

-- 对于MySQL 5.7及以下版本，使用CONCAT函数
UPDATE em_device 
SET wifi_config = CONCAT(
  '{',
  CASE WHEN wifi_bit_rate IS NOT NULL THEN CONCAT('"bitRate":', wifi_bit_rate, ',') ELSE '' END,
  CASE WHEN wifi_freq IS NOT NULL THEN CONCAT('"freq":', wifi_freq, ',') ELSE '' END,
  CASE WHEN wifi_ip IS NOT NULL THEN CONCAT('"ip":"', wifi_ip, '",') ELSE '' END,
  CASE WHEN wifi_key_mgmt IS NOT NULL THEN CONCAT('"keyMgmt":"', wifi_key_mgmt, '",') ELSE '' END,
  CASE WHEN wifi_mac IS NOT NULL THEN CONCAT('"mac":"', wifi_mac, '",') ELSE '' END,
  CASE WHEN wifi_ssid IS NOT NULL THEN CONCAT('"ssid":"', wifi_ssid, '",') ELSE '' END,
  CASE WHEN wifi_state IS NOT NULL THEN CONCAT('"state":"', wifi_state, '",') ELSE '' END,
  CASE WHEN wifi_link_quality IS NOT NULL THEN CONCAT('"linkQuality":"', wifi_link_quality, '",') ELSE '' END,
  CASE WHEN wifi_signal_level IS NOT NULL THEN CONCAT('"signalLevel":"', wifi_signal_level, '",') ELSE '' END,
  CASE WHEN gateway_mac IS NOT NULL THEN CONCAT('"gatewayMac":"', gateway_mac, '"') ELSE '' END,
  '}'
)
WHERE wifi_bit_rate IS NOT NULL 
   OR wifi_freq IS NOT NULL 
   OR wifi_ip IS NOT NULL 
   OR wifi_key_mgmt IS NOT NULL 
   OR wifi_mac IS NOT NULL 
   OR wifi_ssid IS NOT NULL 
   OR wifi_state IS NOT NULL 
   OR wifi_link_quality IS NOT NULL 
   OR wifi_signal_level IS NOT NULL 
   OR gateway_mac IS NOT NULL;

-- 清理JSON格式（移除多余的逗号）
UPDATE em_device 
SET wifi_config = REPLACE(REPLACE(wifi_config, ',}', '}'), '{,', '{')
WHERE wifi_config IS NOT NULL;
```

### 3. 验证数据迁移
```sql
-- 检查迁移结果
SELECT 
  id,
  name,
  wifi_config,
  wifi_ssid,
  wifi_ip,
  wifi_mac
FROM em_device 
WHERE wifi_config IS NOT NULL 
LIMIT 10;

-- 验证JSON格式是否正确
SELECT 
  id,
  name,
  JSON_VALID(wifi_config) as is_valid_json,
  wifi_config
FROM em_device 
WHERE wifi_config IS NOT NULL;
```

### 4. 删除旧字段（确认数据正确后执行）
```sql
-- 备份表（可选）
CREATE TABLE em_device_backup AS SELECT * FROM em_device;

-- 删除旧的WiFi字段
ALTER TABLE em_device DROP COLUMN wifi_bit_rate;
ALTER TABLE em_device DROP COLUMN wifi_freq;
ALTER TABLE em_device DROP COLUMN wifi_ip;
ALTER TABLE em_device DROP COLUMN wifi_key_mgmt;
ALTER TABLE em_device DROP COLUMN wifi_mac;
ALTER TABLE em_device DROP COLUMN wifi_ssid;
ALTER TABLE em_device DROP COLUMN wifi_state;
ALTER TABLE em_device DROP COLUMN wifi_link_quality;
ALTER TABLE em_device DROP COLUMN wifi_signal_level;
ALTER TABLE em_device DROP COLUMN gateway_mac;
```

## 代码更新

### 1. 后端模型更新
- ✅ 更新DeviceAttributes接口
- ✅ 添加WifiConfig接口
- ✅ 更新Device模型类
- ✅ 添加JSON getter/setter

### 2. API接口更新
设备相关的API接口会自动支持新的JSON格式，无需额外修改。

### 3. 前端更新
前端代码需要相应更新以处理新的JSON格式：

```typescript
// 原来的访问方式
device.wifiSsid
device.wifiIp
device.wifiMac

// 新的访问方式
device.wifiConfig?.ssid
device.wifiConfig?.ip
device.wifiConfig?.mac
```

## 使用示例

### 创建设备时设置WiFi配置
```typescript
const device = await Device.create({
  name: '设备001',
  enterpriseId: 1,
  wifiConfig: {
    ssid: 'Factory_WiFi',
    ip: '*************',
    mac: '00:11:22:33:44:55',
    state: 'connected',
    signalLevel: '-40 dBm'
  }
});
```

### 更新WiFi配置
```typescript
// 更新整个配置
await device.update({
  wifiConfig: {
    ssid: 'New_WiFi',
    ip: '*************',
    state: 'connected'
  }
});

// 部分更新（需要先获取现有配置）
const currentConfig = device.wifiConfig || {};
await device.update({
  wifiConfig: {
    ...currentConfig,
    state: 'disconnected'
  }
});
```

### 查询WiFi配置
```typescript
// 获取设备的WiFi配置
const device = await Device.findByPk(1);
const wifiConfig = device.wifiConfig;

if (wifiConfig) {
  console.log('WiFi SSID:', wifiConfig.ssid);
  console.log('WiFi IP:', wifiConfig.ip);
  console.log('连接状态:', wifiConfig.state);
}
```

## 优势

### 1. 灵活性
- 可以轻松添加新的WiFi配置项
- 不需要修改数据库结构
- 支持复杂的嵌套配置

### 2. 可维护性
- 减少了数据库字段数量
- 统一的配置管理
- 更清晰的数据结构

### 3. 扩展性
- 支持不同类型的网络配置
- 可以添加历史配置记录
- 便于版本控制和配置对比

## 注意事项

### 1. 数据备份
在执行迁移前，务必备份现有数据。

### 2. 分步执行
建议分步执行迁移，先添加新字段，验证数据正确后再删除旧字段。

### 3. 应用兼容性
确保前端应用和其他依赖系统都已更新以支持新的数据格式。

### 4. 性能考虑
JSON字段的查询性能可能不如普通字段，如需频繁查询特定WiFi属性，可考虑添加虚拟列或索引。

## 回滚方案

如果需要回滚到原有结构：

```sql
-- 重新添加原有字段
ALTER TABLE em_device ADD COLUMN wifi_ssid VARCHAR(50);
ALTER TABLE em_device ADD COLUMN wifi_ip VARCHAR(15);
-- ... 添加其他字段

-- 从JSON中提取数据
UPDATE em_device 
SET 
  wifi_ssid = JSON_UNQUOTE(JSON_EXTRACT(wifi_config, '$.ssid')),
  wifi_ip = JSON_UNQUOTE(JSON_EXTRACT(wifi_config, '$.ip'))
  -- ... 提取其他字段
WHERE wifi_config IS NOT NULL;

-- 删除JSON字段
ALTER TABLE em_device DROP COLUMN wifi_config;
```
