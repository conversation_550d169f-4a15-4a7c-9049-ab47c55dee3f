# 设备表单必填字段更新报告

## 更新需求
根据业务需求，将设备表单中的特定字段设为必填项，并确保非必填项在未填写时不进行校验。

## 必填字段列表

### 1. 基本信息必填字段
- ✅ **设备名称** (`name`) - 已设为必填
- ✅ **设备编号** (`code`) - 新增必填
- ✅ **设备SN** (`sn`) - 新增必填
- ✅ **MAC地址** (`mac`) - 新增必填

### 2. 机器参数必填字段
- ✅ **机头头距** (`headSpace`) - 新增必填
- ✅ **机头头数** (`headNum`) - 新增必填
- ✅ **机头针数** (`headNeedleNum`) - 新增必填

### 3. 计算参数必填字段
- ✅ **计算头距** (`formularHeadSpace`) - 新增必填
- ✅ **计算头数** (`formularHeadNum`) - 新增必填
- ✅ **计算长度** (`formularLength`) - 新增必填

## 非必填字段（可选）

### 1. 网络信息
- **IP地址** (`ip`) - 可选，有值时验证格式
- **WiFi配置** (`wifiConfig`) - 可选

### 2. 设备配置
- **机型** (`deviceModelId`) - 可选
- **产线** (`productionLineId`) - 可选
- **设备分组** (`groupId`) - 可选
- **厂商** (`vendor`) - 可选
- **电控型号** (`controlModel`) - 可选

### 3. 软件信息
- **显示软件** (`displaySoftware`) - 可选
- **主控软件** (`controlSoftware`) - 可选

### 4. 其他信息
- **备注** (`remark`) - 可选
- **添加方式** (`registerWay`) - 有默认值

## 验证规则更新

### 1. 新增必填验证的字段

#### 设备编号
```typescript
// 修改前
code: [
  { max: 50, message: '设备编号长度不能超过50个字符', trigger: 'blur' }
]

// 修改后
code: [
  { required: true, message: '请输入设备编号', trigger: 'blur' },
  { max: 50, message: '设备编号长度不能超过50个字符', trigger: 'blur' }
]
```

#### 设备SN
```typescript
// 修改前
sn: [
  { max: 100, message: '设备SN长度不能超过100个字符', trigger: 'blur' }
]

// 修改后
sn: [
  { required: true, message: '请输入设备SN', trigger: 'blur' },
  { max: 100, message: '设备SN长度不能超过100个字符', trigger: 'blur' }
]
```

#### MAC地址
```typescript
// 修改前
mac: [
  {
    validator: (rule: any, value: string) => {
      if (value && !deviceUtils.isValidMacAddress(value)) {
        return Promise.reject('MAC地址格式不正确')
      }
      return Promise.resolve()
    },
    trigger: 'blur'
  }
]

// 修改后
mac: [
  { required: true, message: '请输入MAC地址', trigger: 'blur' },
  {
    validator: (rule: any, value: string) => {
      if (value && !deviceUtils.isValidMacAddress(value)) {
        return Promise.reject('MAC地址格式不正确')
      }
      return Promise.resolve()
    },
    trigger: 'blur'
  }
]
```

#### 机器参数字段
```typescript
// 修改前
headSpace: [
  { type: 'number', min: 0, message: '机头头距必须大于等于0', trigger: 'blur' }
]

// 修改后
headSpace: [
  { required: true, message: '请输入机头头距', trigger: 'blur' },
  { type: 'number', min: 0, message: '机头头距必须大于等于0', trigger: 'blur' }
]

// 同样的模式应用于：headNum, headNeedleNum, formularHeadSpace, formularHeadNum, formularLength
```

### 2. 保持可选的字段验证

#### IP地址（格式验证，但不强制必填）
```typescript
ip: [
  {
    validator: (rule: any, value: string) => {
      if (value && !deviceUtils.isValidIpAddress(value)) {
        return Promise.reject('IP地址格式不正确')
      }
      return Promise.resolve()
    },
    trigger: 'blur'
  }
]
```

#### 其他可选字段
```typescript
// 这些字段保持原有的长度限制，但不设为必填
vendor: [
  { max: 50, message: '厂商长度不能超过50个字符', trigger: 'blur' }
],
controlModel: [
  { max: 50, message: '电控型号长度不能超过50个字符', trigger: 'blur' }
],
displaySoftware: [
  { max: 100, message: '显示软件长度不能超过100个字符', trigger: 'blur' }
],
controlSoftware: [
  { max: 100, message: '主控软件长度不能超过100个字符', trigger: 'blur' }
],
remark: [
  { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
]
```

## 用户界面变化

### 1. 必填字段标识
Ant Design Vue会自动为设置了`required: true`的字段显示红色星号（*），用户可以清楚地识别哪些字段是必填的。

### 2. 错误提示优化
- **必填字段**：显示"请输入XXX"的友好提示
- **格式验证**：显示具体的格式要求
- **数值验证**：显示数值范围要求

### 3. 表单提交验证
- 必填字段未填写时，表单无法提交
- 可选字段可以为空，不影响表单提交
- 有值的可选字段仍会进行格式验证

## 业务逻辑说明

### 1. 必填字段的业务意义
- **设备名称、编号、SN、MAC**：设备的唯一标识信息
- **机器参数**：设备的核心技术参数，影响生产计算
- **计算参数**：生产计划和成本计算的关键数据

### 2. 可选字段的灵活性
- **网络配置**：部分设备可能不联网
- **分组归属**：可以后续分配
- **软件信息**：可能在设备配置完成后再填写

### 3. 数据完整性保障
- 核心业务数据必须完整
- 辅助信息允许渐进式完善
- 避免因非关键信息缺失阻塞设备录入

## 验证流程

### 1. 表单提交前验证
```
用户点击提交 
→ 检查所有必填字段 
→ 验证已填写字段的格式 
→ 全部通过后提交 
→ 有错误时显示提示并阻止提交
```

### 2. 实时验证
```
用户输入/选择 
→ 触发字段验证 
→ 显示验证结果 
→ 更新表单状态
```

### 3. 错误处理
```
验证失败 
→ 显示错误信息 
→ 聚焦到错误字段 
→ 用户修正后重新验证
```

## 技术实现细节

### 1. Ant Design Vue表单验证
- 使用`required: true`标记必填字段
- 自动显示红色星号标识
- 支持多重验证规则组合

### 2. 验证规则优先级
```typescript
[
  { required: true, message: '必填提示', trigger: 'blur' },      // 1. 必填验证
  { type: 'number', min: 0, message: '类型验证', trigger: 'blur' }, // 2. 类型验证
  { max: 100, message: '长度验证', trigger: 'blur' }              // 3. 长度验证
]
```

### 3. 条件验证
```typescript
// 只在有值时验证格式
validator: (rule: any, value: string) => {
  if (value && !isValidFormat(value)) {
    return Promise.reject('格式错误')
  }
  return Promise.resolve()
}
```

## 测试验证

### 1. 必填字段测试
- [ ] 所有必填字段未填写时显示错误提示
- [ ] 必填字段填写后错误提示消失
- [ ] 表单提交时验证必填字段

### 2. 可选字段测试
- [ ] 可选字段为空时不显示错误
- [ ] 可选字段有值时进行格式验证
- [ ] 可选字段格式错误时显示提示

### 3. 用户体验测试
- [ ] 必填字段显示红色星号
- [ ] 错误提示清晰易懂
- [ ] 验证时机合适（不过于频繁）

## 总结

设备表单必填字段更新已完成：

1. ✅ **10个必填字段**：设备名称、编号、SN、MAC地址、机头头距、机头头数、机头针数、计算头距、计算头数、计算长度
2. ✅ **可选字段保持灵活**：网络配置、分组归属、软件信息等
3. ✅ **验证规则优化**：必填验证 + 格式验证 + 长度验证
4. ✅ **用户体验提升**：红色星号标识 + 友好错误提示
5. ✅ **业务逻辑合理**：核心数据必填，辅助信息可选

这次更新确保了设备录入时核心业务数据的完整性，同时保持了表单的易用性和灵活性。
