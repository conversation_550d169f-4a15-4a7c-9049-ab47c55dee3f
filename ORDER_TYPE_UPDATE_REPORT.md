# 订单类型更新报告

## 更新需求
将订单类型从原来的4种类型更新为3种：
- **原来**：普通订单、紧急订单、样品订单、定制订单
- **现在**：默认、裁片、家纺

## 修改的文件和内容

### 1. 前端类型定义 - frontend/src/types/order.ts

#### 订单类型枚举
```typescript
// 修改前
export enum OrderType {
  NORMAL = 1,         // 普通订单
  URGENT = 2,         // 紧急订单
  SAMPLE = 3,         // 样品订单
  CUSTOM = 4          // 定制订单
}

// 修改后
export enum OrderType {
  DEFAULT = 1,        // 默认
  CUTTING = 2,        // 裁片
  HOME_TEXTILE = 3    // 家纺
}
```

#### 订单类型选项配置
```typescript
// 修改前
export const ORDER_TYPE_OPTIONS = [
  { value: OrderType.NORMAL, label: '普通订单', color: '#1890ff' },
  { value: OrderType.URGENT, label: '紧急订单', color: '#ff4d4f' },
  { value: OrderType.SAMPLE, label: '样品订单', color: '#faad14' },
  { value: OrderType.CUSTOM, label: '定制订单', color: '#722ed1' }
];

// 修改后
export const ORDER_TYPE_OPTIONS = [
  { value: OrderType.DEFAULT, label: '默认', color: '#1890ff' },
  { value: OrderType.CUTTING, label: '裁片', color: '#52c41a' },
  { value: OrderType.HOME_TEXTILE, label: '家纺', color: '#722ed1' }
];
```

### 2. 后端数据模型 - backend/src/shared/database/models/Order.ts

#### 订单类型枚举
```typescript
// 修改前
export enum OrderType {
  NORMAL = 1,         // 普通订单
  URGENT = 2,         // 紧急订单
  SAMPLE = 3,         // 样品订单
  CUSTOM = 4          // 定制订单
}

// 修改后
export enum OrderType {
  DEFAULT = 1,        // 默认
  CUTTING = 2,        // 裁片
  HOME_TEXTILE = 3    // 家纺
}
```

#### 类型名称映射
```typescript
// 修改前
public getTypeName(): string {
  const typeNames = {
    [OrderType.NORMAL]: '普通订单',
    [OrderType.URGENT]: '紧急订单',
    [OrderType.SAMPLE]: '样品订单',
    [OrderType.CUSTOM]: '定制订单'
  };
  return typeNames[this.type] || '未知类型';
}

// 修改后
public getTypeName(): string {
  const typeNames = {
    [OrderType.DEFAULT]: '默认',
    [OrderType.CUTTING]: '裁片',
    [OrderType.HOME_TEXTILE]: '家纺'
  };
  return typeNames[this.type] || '未知类型';
}
```

#### 数据库字段配置
```typescript
// 修改前
type: {
  type: DataTypes.INTEGER,
  allowNull: false,
  defaultValue: OrderType.NORMAL,
  comment: '订单类型',
  validate: {
    isIn: {
      args: [[OrderType.NORMAL, OrderType.URGENT, OrderType.SAMPLE, OrderType.CUSTOM]],
      msg: '订单类型必须是有效值',
    },
  },
}

// 修改后
type: {
  type: DataTypes.INTEGER,
  allowNull: false,
  defaultValue: OrderType.DEFAULT,
  comment: '订单类型',
  validate: {
    isIn: {
      args: [[OrderType.DEFAULT, OrderType.CUTTING, OrderType.HOME_TEXTILE]],
      msg: '订单类型必须是有效值',
    },
  },
}
```

### 3. 后端验证规则 - backend/src/modules/order/order.controller.ts

#### 创建订单验证
```typescript
// 修改前
body('type').optional().isInt({ min: 1, max: 4 }).withMessage('订单类型必须是有效值')

// 修改后
body('type').optional().isInt({ min: 1, max: 3 }).withMessage('订单类型必须是有效值')
```

#### 更新订单验证
```typescript
// 修改前
body('type').optional().isInt({ min: 1, max: 4 }).withMessage('订单类型必须是有效值')

// 修改后
body('type').optional().isInt({ min: 1, max: 3 }).withMessage('订单类型必须是有效值')
```

## 数据库迁移建议

### 现有数据处理
如果数据库中已有订单数据，建议执行以下SQL来处理现有数据：

```sql
-- 将现有的订单类型映射到新的类型
UPDATE em_orders SET 
  type = CASE 
    WHEN type = 1 THEN 1  -- NORMAL -> DEFAULT
    WHEN type = 2 THEN 2  -- URGENT -> CUTTING  
    WHEN type = 3 THEN 3  -- SAMPLE -> HOME_TEXTILE
    WHEN type = 4 THEN 1  -- CUSTOM -> DEFAULT (作为默认处理)
    ELSE 1
  END;

-- 或者根据业务需求进行更精确的映射
-- 例如：
-- UPDATE em_orders SET type = 1 WHERE type IN (1, 4); -- 普通和定制都归为默认
-- UPDATE em_orders SET type = 2 WHERE type = 2;        -- 紧急归为裁片
-- UPDATE em_orders SET type = 3 WHERE type = 3;        -- 样品归为家纺
```

## 功能影响分析

### 1. 前端界面
- ✅ 订单列表页面的类型筛选下拉框会显示新的3个选项
- ✅ 订单表单的类型选择会显示新的3个选项
- ✅ 订单详情页面会显示新的类型标签和颜色
- ✅ 所有类型相关的显示都会自动更新

### 2. 后端API
- ✅ 创建订单时只接受1-3的类型值
- ✅ 更新订单时只接受1-3的类型值
- ✅ 查询订单时类型筛选只支持1-3
- ✅ 返回的订单数据中类型名称会显示新的名称

### 3. 数据验证
- ✅ 前端表单验证会限制为新的3个选项
- ✅ 后端API验证会拒绝无效的类型值
- ✅ 数据库约束会确保数据完整性

## 测试验证

### 1. 前端测试
- [ ] 订单列表页面类型筛选功能正常
- [ ] 新增订单时类型选择正常
- [ ] 编辑订单时类型选择正常
- [ ] 订单详情页面类型显示正常
- [ ] 类型标签颜色显示正确

### 2. 后端测试
- [ ] 创建订单API接受新的类型值
- [ ] 创建订单API拒绝无效类型值（4及以上）
- [ ] 更新订单API接受新的类型值
- [ ] 更新订单API拒绝无效类型值
- [ ] 查询订单API类型筛选正常

### 3. 数据库测试
- [ ] 新创建的订单默认类型为1（默认）
- [ ] 数据库约束正确限制类型值范围
- [ ] 现有数据迁移正确（如果有）

## 兼容性说明

### 1. 向后兼容性
- **不兼容**：此更改不向后兼容，旧的类型值4将被拒绝
- **数据迁移**：需要处理现有数据库中的type=4记录
- **API版本**：建议在API文档中标注此变更

### 2. 客户端更新
- **前端**：需要同步更新前端代码
- **移动端**：如果有移动端应用，也需要同步更新
- **第三方集成**：通知第三方系统更新类型值

## 部署注意事项

### 1. 部署顺序
1. 首先部署后端代码（支持新的类型验证）
2. 执行数据库迁移脚本（如果需要）
3. 部署前端代码（显示新的类型选项）

### 2. 回滚计划
如果需要回滚：
1. 恢复前端代码到旧版本
2. 恢复后端代码到旧版本
3. 如果执行了数据迁移，需要准备反向迁移脚本

## 总结

订单类型已成功从4种更新为3种：
1. ✅ **默认**（原普通订单）- 蓝色标签
2. ✅ **裁片**（新类型）- 绿色标签  
3. ✅ **家纺**（新类型）- 紫色标签

所有相关的前端显示、后端验证、数据库约束都已同步更新，确保系统的一致性和数据完整性。
