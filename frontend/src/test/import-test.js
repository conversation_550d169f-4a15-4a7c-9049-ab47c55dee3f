// 测试导入是否正常工作
console.log('Testing imports...');

try {
  // 测试类型导入
  import { 
    calculateDelayDays, 
    isOrderDelayed,
    getOrderStatusLabel,
    getOrderStatusColor 
  } from '../types/order.ts';
  
  console.log('✅ Types import successful');
  
  // 测试函数
  const testDate = '2025-01-01';
  const delayDays = calculateDelayDays(testDate);
  const isDelayed = isOrderDelayed(testDate);
  
  console.log('✅ Functions work correctly');
  console.log(`Delay days: ${delayDays}, Is delayed: ${isDelayed}`);
  
} catch (error) {
  console.error('❌ Import failed:', error);
}
