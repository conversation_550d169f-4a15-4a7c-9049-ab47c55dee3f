/**
 * 订单管理API
 * {{CHENGQI: Action: Added; Timestamp: 2025-07-28 19:00:00 +08:00; Reason: Shrimp Task ID: #eef4c6a0-6630-40a3-a4ff-537e145143f4, 创建前端订单API封装; Principle_Applied: API封装;}}
 * {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 14:00:00 +08:00; Reason: Shrimp Task ID: #fa076766-4dc5-42e1-8100-77ad3fbeb5fa, 添加生产历史API接口; Principle_Applied: API封装;}}
 */

import { http } from './request';
import type {
  Order,
  OrderListQuery,
  OrderListResponse,
  CreateOrderRequest,
  UpdateOrderRequest,
  UpdateOrderStatusRequest,
  OrderSearchOptions,
  DeviceOrderSequence,
  SequenceListQuery,
  SequenceListResponse,
  CreateSequenceRequest,
  UpdateSequenceRequest,
  ReorderSequencesRequest,
  ProductionQuantityResult,
  CalculateProductionQuantityRequest,
  BatchCreateSequenceRequest
} from '../types/order';

/**
 * 订单管理API
 */
export const orderApi = {
  /**
   * 获取订单列表
   */
  getOrderList: (params: OrderListQuery): Promise<OrderListResponse> => {
    // 处理状态数组参数
    const processedParams = { ...params };
    if (Array.isArray(params.status)) {
      processedParams.status = params.status.join(',') as any;
    }
    return http.get('/orders', { params: processedParams });
  },

  /**
   * 获取订单详情
   */
  getOrderById: (id: number): Promise<Order> => {
    return http.get(`/orders/${id}`);
  },

  /**
   * 创建订单
   */
  createOrder: (data: CreateOrderRequest): Promise<Order> => {
    return http.post('/orders', data);
  },

  /**
   * 更新订单
   */
  updateOrder: (id: number, data: UpdateOrderRequest): Promise<Order> => {
    return http.put(`/orders/${id}`, data);
  },

  /**
   * 删除订单
   */
  deleteOrder: (id: number): Promise<void> => {
    return http.delete(`/orders/${id}`);
  },

  /**
   * 批量删除订单
   */
  batchDeleteOrders: async (ids: number[]): Promise<void> => {
    await Promise.all(ids.map(id => orderApi.deleteOrder(id)));
  },

  /**
   * 更新订单状态
   */
  updateOrderStatus: (id: number, data: UpdateOrderStatusRequest): Promise<Order> => {
    return http.put(`/orders/${id}/status`, data);
  },

  /**
   * 获取订单搜索选项
   */
  getSearchOptions: (): Promise<OrderSearchOptions> => {
    return http.get('/orders/search-options');
  }
};

/**
 * 设备排产管理API
 */
export const sequenceApi = {
  /**
   * 获取排产记录列表
   */
  getSequenceList: (params: SequenceListQuery): Promise<SequenceListResponse> => {
    return http.get('/sequences', { params });
  },

  /**
   * 创建排产记录
   */
  createSequence: (data: CreateSequenceRequest): Promise<DeviceOrderSequence> => {
    return http.post('/sequences', data);
  },

  /**
   * 批量创建排产记录
   */
  batchCreateSequences: (data: BatchCreateSequenceRequest): Promise<DeviceOrderSequence[]> => {
    return http.post('/sequences/batch', data);
  },

  /**
   * 更新排产记录
   */
  updateSequence: (id: number, data: UpdateSequenceRequest): Promise<DeviceOrderSequence> => {
    return http.put(`/sequences/${id}`, data);
  },

  /**
   * 删除排产记录
   */
  deleteSequence: (id: number): Promise<void> => {
    return http.delete(`/sequences/${id}`);
  },

  /**
   * 根据订单获取排产记录
   */
  getSequencesByOrder: (orderId: number): Promise<DeviceOrderSequence[]> => {
    return http.get(`/sequences/order/${orderId}`);
  },

  /**
   * 根据设备获取排产队列
   */
  getSequencesByDevice: (deviceId: number): Promise<DeviceOrderSequence[]> => {
    return http.get(`/sequences/device/${deviceId}`);
  },

  /**
   * 重新排序排产记录
   */
  reorderSequences: (data: ReorderSequencesRequest): Promise<void> => {
    return http.put('/sequences/reorder', data);
  },

  /**
   * 计算生产数量
   */
  calculateProductionQuantity: (data: CalculateProductionQuantityRequest): Promise<ProductionQuantityResult> => {
    return http.post('/sequences/calculate', data);
  },

  /**
   * 批量删除排产记录
   */
  batchDeleteSequences: async (ids: number[]): Promise<void> => {
    await Promise.all(ids.map(id => sequenceApi.deleteSequence(id)));
  },

  /**
   * 开始生产
   */
  startProduction: (id: number, data?: { remark?: string }): Promise<DeviceOrderSequence> => {
    return http.put(`/sequences/${id}/start`, data);
  },

  /**
   * 完成生产
   */
  completeProduction: (id: number, data?: { actualQuantity?: number; remark?: string }): Promise<DeviceOrderSequence> => {
    return http.put(`/sequences/${id}/complete`, data);
  },

  /**
   * 更新生产进度
   */
  updateProgress: (id: number, data: { progress: number; remark?: string }): Promise<DeviceOrderSequence> => {
    return http.put(`/sequences/${id}/progress`, data);
  },

  /**
   * 获取生产统计数据
   */
  getProductionStatistics: (params?: { startDate?: string; endDate?: string }): Promise<any> => {
    return http.get('/sequences/statistics', { params });
  },

  /**
   * 获取生产历史记录
   */
  getProductionHistory: (params: any): Promise<any> => {
    const { sequenceId, ...otherParams } = params;
    if (sequenceId) {
      return http.get(`/sequences/${sequenceId}/history`, { params: otherParams });
    } else {
      return http.get('/sequences/history', { params: otherParams });
    }
  }
};

// 导出生产历史查询函数供组件使用
export const getProductionHistory = sequenceApi.getProductionHistory;

/**
 * 文件上传API
 */
export const orderFileApi = {
  /**
   * 上传订单文件
   */
  uploadOrderFile: (file: File): Promise<{ url: string; filename: string }> => {
    const formData = new FormData();
    formData.append('file', file);
    
    return http.post('/upload/order', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 批量上传订单文件
   */
  batchUploadOrderFiles: async (files: File[]): Promise<Array<{ url: string; filename: string }>> => {
    const uploadPromises = files.map(file => orderFileApi.uploadOrderFile(file));
    return Promise.all(uploadPromises);
  },

  /**
   * 删除订单文件
   */
  deleteOrderFile: (fileUrl: string): Promise<void> => {
    return http.delete('/upload/order', { 
      data: { fileUrl } 
    });
  },

  /**
   * 获取文件预览URL
   */
  getFilePreviewUrl: (fileUrl: string): string => {
    // 如果是相对路径，添加基础URL
    if (fileUrl.startsWith('/')) {
      return `${import.meta.env.VITE_API_BASE_URL}${fileUrl}`;
    }
    return fileUrl;
  },

  /**
   * 下载订单文件
   */
  downloadOrderFile: (fileUrl: string, filename?: string): void => {
    const link = document.createElement('a');
    link.href = orderFileApi.getFilePreviewUrl(fileUrl);
    link.download = filename || fileUrl.split('/').pop() || 'download';
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

/**
 * 订单工具类
 */
export class OrderUtils {
  /**
   * 格式化订单编号显示
   */
  static formatOrderCode(code?: string): string {
    if (!code) return '-';
    return code.toUpperCase();
  }

  /**
   * 格式化客户名称显示
   */
  static formatCustomerName(customerName?: string): string {
    if (!customerName) return '-';
    return customerName.trim();
  }

  /**
   * 格式化业务员显示
   */
  static formatSalesman(salesman?: string): string {
    if (!salesman) return '-';
    return salesman.trim();
  }

  /**
   * 计算订单延期天数
   */
  static calculateDelayDays(deliveryDate?: string): number {
    if (!deliveryDate) return 0;

    const delivery = new Date(deliveryDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    delivery.setHours(0, 0, 0, 0);

    const diffTime = today.getTime() - delivery.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays > 0 ? diffDays : 0;
  }

  /**
   * 检查订单是否延期
   */
  static isOrderDelayed(deliveryDate?: string): boolean {
    return OrderUtils.calculateDelayDays(deliveryDate) > 0;
  }

  /**
   * 获取订单紧急程度
   */
  static getOrderUrgency(deliveryDate?: string): 'high' | 'medium' | 'low' {
    if (!deliveryDate) return 'low';

    const delivery = new Date(deliveryDate);
    const today = new Date();
    const diffTime = delivery.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return 'high'; // 已延期
    if (diffDays <= 3) return 'high'; // 3天内
    if (diffDays <= 7) return 'medium'; // 7天内
    return 'low';
  }

  /**
   * 获取紧急程度颜色
   */
  static getUrgencyColor(urgency: 'high' | 'medium' | 'low'): string {
    const colorMap = {
      high: '#ff4d4f',
      medium: '#faad14',
      low: '#52c41a'
    };
    return colorMap[urgency];
  }

  /**
   * 验证订单编号格式
   */
  static validateOrderCode(code: string): boolean {
    if (!code || code.trim().length === 0) return false;
    // 订单编号应该是字母数字组合，长度在3-50之间
    const regex = /^[A-Za-z0-9\-_]{3,50}$/;
    return regex.test(code.trim());
  }

  /**
   * 验证客户名称
   */
  static validateCustomerName(customerName: string): boolean {
    if (!customerName || customerName.trim().length === 0) return false;
    return customerName.trim().length <= 200;
  }

  /**
   * 验证业务员名称
   */
  static validateSalesman(salesman: string): boolean {
    if (!salesman || salesman.trim().length === 0) return false;
    return salesman.trim().length <= 100;
  }

  /**
   * 验证订单数量
   */
  static validateOrderQuantity(quantity: number): boolean {
    return Number.isInteger(quantity) && quantity >= 0;
  }

  /**
   * 格式化文件大小显示
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取文件扩展名
   */
  static getFileExtension(filename: string): string {
    return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
  }

  /**
   * 检查文件类型是否允许
   */
  static isAllowedFileType(filename: string): boolean {
    const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'zip', 'rar'];
    const extension = OrderUtils.getFileExtension(filename);
    return allowedExtensions.includes(extension);
  }

  /**
   * 生成订单摘要
   */
  static generateOrderSummary(order: Order): string {
    const parts = [];

    if (order.code) parts.push(`订单号: ${order.code}`);
    if (order.customerName) parts.push(`客户: ${order.customerName}`);
    if (order.orderQuantity) parts.push(`数量: ${order.orderQuantity}`);

    return parts.join(' | ') || '订单信息';
  }
}

/**
 * 排产工具类
 */
export class SequenceUtils {
  /**
   * 验证计算公式格式
   */
  static validateFormula(formula: string, unitName: string = '件'): boolean {
    if (!formula || formula.trim().length === 0) return false;
    const regex = new RegExp(`^\\d+${unitName}=\\d+头$`);
    return regex.test(formula.trim());
  }

  /**
   * 解析计算公式
   */
  static parseFormula(formula: string, unitName: string = '件'): { pieces: number; heads: number } | null {
    if (!SequenceUtils.validateFormula(formula, unitName)) return null;

    const regex = new RegExp(`^(\\d+)${unitName}=(\\d+)头$`);
    const match = formula.match(regex);
    if (!match) return null;

    return {
      pieces: parseInt(match[1]),
      heads: parseInt(match[2])
    };
  }

  /**
   * 计算生产数量
   */
  static calculateProductionQuantity(lathesNum: number, deviceHeadNum: number, formula: string): number {
    if (!lathesNum || !deviceHeadNum) return 0;

    const parsed = SequenceUtils.parseFormula(formula);
    if (!parsed) {
      // 默认计算方式：车数 * 设备头数
      return lathesNum * deviceHeadNum;
    }

    return Math.floor((lathesNum * deviceHeadNum * parsed.pieces) / parsed.heads);
  }

  /**
   * 验证车数
   */
  static validateLathesNum(lathesNum: number): boolean {
    return Number.isInteger(lathesNum) && lathesNum >= 0;
  }

  /**
   * 验证生产排序
   */
  static validateProductionSequence(sequence: number): boolean {
    return Number.isInteger(sequence) && sequence >= 1;
  }

  /**
   * 生成排产摘要
   */
  static generateSequenceSummary(sequence: DeviceOrderSequence): string {
    const parts = [];

    if (sequence.order?.code) parts.push(`订单: ${sequence.order.code}`);
    if (sequence.device?.name) parts.push(`设备: ${sequence.device.name}`);
    if (sequence.pattern?.name) parts.push(`花样: ${sequence.pattern.name}`);
    if (sequence.productionQuantity) parts.push(`数量: ${sequence.productionQuantity}`);

    return parts.join(' | ') || '排产信息';
  }

  /**
   * 检查排产是否可以拖拽排序
   */
  static canReorder(sequences: DeviceOrderSequence[]): boolean {
    // 只有等待中的排产记录才能拖拽排序
    return sequences.every(seq => seq.status === 0); // DeviceOrderSequenceStatus.WAITING
  }

  /**
   * 生成新的排序数组
   */
  static generateNewSequenceOrder(sequences: DeviceOrderSequence[], fromIndex: number, toIndex: number): number[] {
    const items = [...sequences];
    const [removed] = items.splice(fromIndex, 1);
    items.splice(toIndex, 0, removed);
    return items.map(item => item.id);
  }

  /**
   * 计算排产总时长（估算）
   */
  static estimateProductionTime(sequences: DeviceOrderSequence[]): number {
    // 简单估算：每个排产记录按生产数量计算时间（分钟）
    return sequences.reduce((total, seq) => {
      const quantity = seq.productionQuantity || 0;
      // 假设每件产品需要1分钟（可根据实际情况调整）
      return total + quantity;
    }, 0);
  }

  /**
   * 格式化生产时长显示
   */
  static formatProductionTime(minutes: number): string {
    if (minutes < 60) {
      return `${minutes}分钟`;
    }

    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours < 24) {
      return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`;
    }

    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;

    return remainingHours > 0 ? `${days}天${remainingHours}小时` : `${days}天`;
  }
}
