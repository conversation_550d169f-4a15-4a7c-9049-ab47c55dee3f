<!--
  设备列表组件 - 生产计划模块
  {{CHENGQI: Action: Added; Timestamp: 2025-07-28 20:30:00 +08:00; Reason: Shrimp Task ID: #44125197-df94-40f0-93a5-baa548253f57, 创建设备列表组件; Principle_Applied: 组件化设计、API复用;}}
-->

<template>
  <div class="device-list">
    <!-- 搜索区域 -->
    <div class="search-section">
      <a-input-search
        v-model:value="searchKeyword"
        placeholder="搜索设备名称、编号"
        allow-clear
        @search="handleSearch"
        @press-enter="handleSearch"
        class="search-input"
      />
    </div>

    <!-- 设备列表 -->
    <div class="device-list-container">
      <a-spin :spinning="loading">
        <a-list
          :data-source="filteredDevices"
          :locale="{ emptyText: '暂无设备数据' }"
          class="device-list-content"
        >
          <template #renderItem="{ item: device }">
            <a-list-item
              :class="[
                'device-item',
                { 'device-item-selected': selectedDeviceId === device.id }
              ]"
              @click="handleSelectDevice(device)"
            >
              <div class="device-info">
                <!-- 设备基本信息 -->
                <div class="device-header">
                  <div class="device-name">
                    <span class="name-text">{{ device.name }}</span>
                    <a-tag
                      v-if="device.code"
                      size="small"
                      color="blue"
                      class="device-code"
                    >
                      {{ device.code }}
                    </a-tag>
                  </div>
                  <div class="device-status">
                    <a-badge
                      :status="getDeviceStatusType(device)"
                      :text="getDeviceStatusText(device)"
                    />
                  </div>
                </div>

                <!-- 设备详细信息 -->
                <div class="device-details">
                  <div class="detail-item" v-if="device.deviceModelInfo">
                    <span class="label">机型:</span>
                    <span class="value">{{ device.deviceModelInfo.name }}</span>
                  </div>
                  <div class="detail-item" v-if="device.headNum">
                    <span class="label">头数:</span>
                    <span class="value">{{ device.headNum }}头</span>
                  </div>
                  <div class="detail-item" v-if="device.ip">
                    <span class="label">IP:</span>
                    <span class="value">{{ device.ip }}</span>
                  </div>
                </div>

                <!-- 选中指示器 -->
                <div v-if="selectedDeviceId === device.id" class="selected-indicator">
                  <!-- <CheckCircleFilled class="selected-icon" /> -->
                </div>
              </div>
            </a-list-item>
          </template>
        </a-list>
      </a-spin>
    </div>

    <!-- 底部统计信息 -->
    <div class="device-stats">
      <a-space>
        <span class="stat-item">
          总计: {{ devices.length }}台
        </span>
        <span class="stat-item">
          在线: {{ onlineDeviceCount }}台
        </span>
        <span class="stat-item">
          离线: {{ offlineDeviceCount }}台
        </span>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import { CheckCircleFilled } from '@ant-design/icons-vue'
import { deviceApi } from '../../api/device'
import type { Device, DeviceListQuery } from '../../types/device'

// Props
interface Props {
  autoSelectFirst?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoSelectFirst: true
})

// Emits
interface Emits {
  (e: 'device-selected', device: Device | null): void
  (e: 'devices-loaded', devices: Device[]): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const devices = ref<Device[]>([])
const selectedDeviceId = ref<number | null>(null)
const searchKeyword = ref('')

// 计算属性
const filteredDevices = computed(() => {
  if (!searchKeyword.value) {
    return devices.value
  }
  
  const keyword = searchKeyword.value.toLowerCase()
  return devices.value.filter(device => 
    device.name.toLowerCase().includes(keyword) ||
    device.code?.toLowerCase().includes(keyword) ||
    device.sn?.toLowerCase().includes(keyword)
  )
})

const onlineDeviceCount = computed(() => {
  return devices.value.filter(device => isDeviceOnline(device)).length
})

const offlineDeviceCount = computed(() => {
  return devices.value.length - onlineDeviceCount.value
})

// 设备状态判断
const isDeviceOnline = (device: Device): boolean => {
  // 简单的在线判断逻辑，可根据实际需求调整
  return !!device.ip && device.wifiState !== 'DISCONNECTED'
}

const getDeviceStatusType = (device: Device): 'success' | 'error' | 'default' | 'processing' | 'warning' => {
  if (isDeviceOnline(device)) {
    return 'success'
  }
  return 'error'
}

const getDeviceStatusText = (device: Device): string => {
  if (isDeviceOnline(device)) {
    return '在线'
  }
  return '离线'
}

// 方法
const fetchDevices = async () => {
  try {
    loading.value = true

    // 使用分页方式获取所有设备
    let allDevices: Device[] = []
    let currentPage = 1
    const pageSize = 100 // 使用最大允许的pageSize
    let hasMore = true

    while (hasMore) {
      const query: DeviceListQuery = {
        page: currentPage,
        pageSize: pageSize
      }

      const response = await deviceApi.getDeviceList(query)
      const pageDevices = response.devices || []
      allDevices = [...allDevices, ...pageDevices]

      // 检查是否还有更多数据
      hasMore = pageDevices.length === pageSize && currentPage < (response.totalPages || 1)
      currentPage++
    }

    devices.value = allDevices
    emit('devices-loaded', devices.value)

    // 自动选择第一个设备
    if (props.autoSelectFirst && devices.value.length > 0 && !selectedDeviceId.value) {
      handleSelectDevice(devices.value[0])
    }

  } catch (error) {
    console.error('获取设备列表失败:', error)
    message.error('获取设备列表失败')
    devices.value = []
  } finally {
    loading.value = false
  }
}

const handleSelectDevice = (device: Device) => {
  selectedDeviceId.value = device.id
  emit('device-selected', device)
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
  console.log('搜索设备:', searchKeyword.value)
}

// 暴露方法给父组件
const refreshDevices = () => {
  fetchDevices()
}

const clearSelection = () => {
  selectedDeviceId.value = null
  emit('device-selected', null)
}

// 生命周期
onMounted(() => {
  fetchDevices()
})

// 暴露给父组件的方法
defineExpose({
  refreshDevices,
  clearSelection,
  selectedDevice: computed(() => 
    devices.value.find(d => d.id === selectedDeviceId.value) || null
  )
})
</script>

<style scoped>
.device-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
}

.device-list-container {
  flex: 1;
  overflow: hidden;
}

.device-list-content {
  height: 100%;
  overflow-y: auto;
}

.device-item {
  padding: 12px !important;
  margin-bottom: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.device-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
}

.device-item-selected {
  border-color: #1890ff !important;
  background-color: #f6ffed !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2) !important;
}

.device-info {
  width: 100%;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.device-name {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-text {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.device-code {
  font-size: 12px;
}

.device-status {
  flex-shrink: 0;
}

.device-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.label {
  color: #8c8c8c;
  margin-right: 4px;
  min-width: 30px;
}

.value {
  color: #595959;
  flex: 1;
}

.selected-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
}

.selected-icon {
  color: #52c41a;
  font-size: 16px;
}

.device-stats {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  font-size: 12px;
  color: #8c8c8c;
}

/* 滚动条样式 */
.device-list-content :deep(.ant-list-items) {
  padding-right: 4px;
}

.device-list-content::-webkit-scrollbar {
  width: 4px;
}

.device-list-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.device-list-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.device-list-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
