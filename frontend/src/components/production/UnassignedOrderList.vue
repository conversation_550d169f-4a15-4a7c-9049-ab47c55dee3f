<!--
  待分配订单列表组件 - 生产计划模块

  功能特性：
  - 支持搜索和筛选订单
  - 拖拽分配订单到设备
  - 响应式布局设计
  - 防抖搜索优化
  - 可访问性支持

  {{CHENGQI: Action: Added; Timestamp: 2025-07-28 21:00:00 +08:00; Reason: Shrimp Task ID: #c79fe2e7-0564-486c-aaa0-368c1e2a55ad, 创建待分配订单列表组件; Principle_Applied: 组件化设计、拖拽功能;}}
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 XX:XX:XX +08:00; Reason: 优化布局和用户体验，添加响应式设计和可访问性支持; Principle_Applied: 用户体验优化、响应式设计、可访问性;}}
-->

<template>
  <div class="unassigned-order-list">
    <!-- 搜索和筛选区域 -->
    <div class="search-filter-section">
      <a-input-search
        v-model:value="searchKeyword"
        placeholder="搜索订单号、客户名"
        allow-clear
        @search="handleSearch"
        @press-enter="handleSearch"
        class="search-input"
      />
      <a-space class="filter-controls">
        <a-select
          v-model:value="filterType"
          placeholder="订单类型"
          allow-clear
          style="width: 100px"
          @change="handleFilter"
        >
          <a-select-option :value="OrderType.DEFAULT">默认</a-select-option>
          <a-select-option :value="OrderType.CUTTING">裁片</a-select-option>
          <a-select-option :value="OrderType.HOME_TEXTILE">家纺</a-select-option>
        </a-select>

        <a-button
          size="small"
          @click="handleRefresh"
          :loading="loading"
          title="刷新订单列表"
          :aria-label="loading ? '正在刷新订单列表' : '刷新订单列表'"
        >
          <template #icon>
            <ReloadOutlined />
          </template>
        </a-button>
      </a-space>
    </div>

    <!-- 订单列表 -->
    <div
      class="order-list-container"
      :class="{ 'drop-zone-active': isDragOverUnassigned }"
      @drop="handleDropToUnassigned"
      @dragover="handleDragOverUnassigned"
      @dragenter="handleDragEnterUnassigned"
      @dragleave="handleDragLeaveUnassigned"
    >
      <a-spin :spinning="loading">
        <div class="order-list">
          <div
            v-for="order in filteredOrders"
            :key="order.id"
            class="order-card"
            draggable="true"
            @dragstart="handleDragStart($event, order)"
            @dragend="handleDragEnd"
          >
            <!-- 拖拽手柄 -->
            <div class="drag-handle">
              <DragOutlined />
            </div>

            <!-- 订单信息 -->
            <div class="order-info">
              <!-- 订单头部 -->
              <div class="order-header">
                <div class="order-code">
                  <span class="code-text">{{ order.code || `订单${order.id}` }}</span>
                  <a-tag
                    :color="getOrderTypeColor(order.type)"
                    size="small"
                    class="order-type"
                  >
                    {{ getOrderTypeText(order.type) }}
                  </a-tag>
                </div>
                <div class="order-actions">
                  <a-space size="small">
                    <a-button
                      type="primary"
                      size="small"
                      @click.stop="showSplitModal(order)"
                      :disabled="!canSplitOrder(order)"
                      title="拆分订单到多个设备"
                    >
                      <ScissorOutlined />
                      拆分
                    </a-button>
                    <div class="order-status">
                      <a-tag color="orange" size="small">
                        {{ getOrderStatusText(order.status) }}
                      </a-tag>
                    </div>
                  </a-space>
                </div>
              </div>

              <!-- 主要信息区域 -->
              <div class="order-main-info">
                <!-- 左侧信息 -->
                <div class="info-left">
                  <div class="info-row">
                    <UserOutlined class="info-icon" />
                    <span class="info-text">{{ order.customerName || '未指定客户' }}</span>
                    <span v-if="order.salesman" class="info-separator">|</span>
                    <ContactsOutlined v-if="order.salesman" class="info-icon" />
                    <span v-if="order.salesman" class="info-text">{{ order.salesman }}</span>
                  </div>
                  <div class="info-row" v-if="order.orderDate || order.deliveryDate">
                    <CalendarOutlined v-if="order.orderDate" class="info-icon" />
                    <span v-if="order.orderDate" class="info-text">下单: {{ formatDate(order.orderDate) }}</span>
                    <span v-if="order.orderDate && order.deliveryDate" class="info-separator">|</span>
                    <ClockCircleOutlined v-if="order.deliveryDate" class="info-icon" />
                    <span v-if="order.deliveryDate" class="info-text delivery-date">交期: {{ formatDate(order.deliveryDate) }}</span>
                  </div>
                </div>

                <!-- 右侧信息 -->
                <div class="info-right">
                  <div class="info-row" v-if="order.orderQuantity || (order.patternInfo && order.patternInfo.length > 0)">
                    <span v-if="order.orderQuantity" class="info-label">数量:</span>
                    <span v-if="order.orderQuantity" class="info-value">{{ order.orderQuantity }}</span>
                    <span v-if="order.orderQuantity && order.patternInfo && order.patternInfo.length > 0" class="info-separator">|</span>
                    <span v-if="order.patternInfo && order.patternInfo.length > 0" class="info-label">花样:</span>
                    <span v-if="order.patternInfo && order.patternInfo.length > 0" class="info-value">{{ order.patternInfo.length }}个</span>
                  </div>
                </div>
              </div>

              <!-- 备注信息 -->
              <div class="order-remark" v-if="order.remark">
                <FileTextOutlined class="info-icon" />
                <span class="info-text remark-text">{{ order.remark }}</span>
              </div>
            </div>

            <!-- 拖拽状态指示 -->
            <div class="drag-indicator" v-show="isDragging && draggedOrderId === order.id">
              <ArrowRightOutlined />
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="filteredOrders.length === 0 && !loading" class="empty-state">
            <a-empty
              :description="searchKeyword || filterType !== undefined ? '没有找到匹配的订单' : '暂无待分配订单'"
            >
              <template #image>
                <div class="empty-icon">📋</div>
              </template>
            </a-empty>
          </div>
        </div>
      </a-spin>
    </div>

    <!-- 底部统计 -->
    <div class="order-stats">
      <a-space>
        <span class="stat-item">
          总计: {{ orders.length }}个订单
        </span>
        <span class="stat-item">
          筛选: {{ filteredOrders.length }}个
        </span>
      </a-space>
    </div>

    <!-- 订单拆分模态框 -->
    <OrderSplitModal
      :visible="splitModalVisible"
      :order="selectedOrder"
      @success="handleSplitSuccess"
      @cancel="handleSplitCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  DragOutlined,
  UserOutlined,
  ContactsOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  ArrowRightOutlined,
  ScissorOutlined
} from '@ant-design/icons-vue'
import { orderApi, sequenceApi } from '../../api/order'
import type { Order, OrderListQuery, DeviceOrderSequence } from '../../types/order'
import { OrderType, OrderStatus } from '../../types/order'
import { formatDateTime } from '../../utils/date'
import OrderSplitModal from '../order/OrderSplitModal.vue'

// Emits
interface Emits {
  (e: 'orders-loaded', orders: Order[]): void
  (e: 'drag-start', order: Order): void
  (e: 'drag-end'): void
  (e: 'sequence-removed', sequenceId: number): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const orders = ref<Order[]>([])
const searchKeyword = ref('')
const filterType = ref<OrderType | undefined>(undefined)
const isDragging = ref(false)
const draggedOrderId = ref<number | null>(null)
const isDragOverUnassigned = ref(false)

// 拆分模态框相关状态
const splitModalVisible = ref(false)
const selectedOrder = ref<Order | null>(null)

// 计算属性
const filteredOrders = computed(() => {
  let result = orders.value

  // 搜索过滤
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(order =>
      order.code?.toLowerCase().includes(keyword) ||
      order.customerName?.toLowerCase().includes(keyword) ||
      order.salesman?.toLowerCase().includes(keyword)
    )
  }

  // 类型过滤
  if (filterType.value !== undefined) {
    result = result.filter(order => order.type === filterType.value)
  }

  return result
})

// 工具方法
const getOrderTypeText = (type: OrderType): string => {
  const typeMap = {
    [OrderType.DEFAULT]: '默认',
    [OrderType.CUTTING]: '裁片',
    [OrderType.HOME_TEXTILE]: '家纺'
  }
  return typeMap[type] || '未知'
}

const getOrderTypeColor = (type: OrderType): string => {
  const colorMap = {
    [OrderType.DEFAULT]: 'blue',
    [OrderType.CUTTING]: 'green',
    [OrderType.HOME_TEXTILE]: 'purple'
  }
  return colorMap[type] || 'default'
}

const getOrderStatusText = (status: OrderStatus): string => {
  const statusMap = {
    [OrderStatus.UNSCHEDULED]: '未排产',
    [OrderStatus.SCHEDULED]: '已排产',
    [OrderStatus.IN_PRODUCTION]: '生产中',
    [OrderStatus.COMPLETED]: '已完成'
  }
  return statusMap[status] || '未知'
}

const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  return formatDateTime(dateString, 'YYYY-MM-DD')
}

// 拆分相关方法
const canSplitOrder = (order: Order): boolean => {
  // 只有未排产或部分排产的订单可以拆分
  return order.status === OrderStatus.UNSCHEDULED && !!(order.orderQuantity && order.orderQuantity > 0)
}

const showSplitModal = (order: Order) => {
  selectedOrder.value = order
  splitModalVisible.value = true
}

const handleSplitSuccess = () => {
  splitModalVisible.value = false
  selectedOrder.value = null
  // 刷新订单列表
  fetchUnassignedOrders()
  // 通知父组件刷新
  emit('orders-loaded', orders.value)
  message.success('订单拆分成功')
}

const handleSplitCancel = () => {
  splitModalVisible.value = false
  selectedOrder.value = null
}

// 数据获取
const fetchUnassignedOrders = async () => {
  try {
    loading.value = true

    // 使用分页方式获取所有未分配订单
    let allOrders: Order[] = []
    let currentPage = 1
    const pageSize = 100 // 使用最大允许的pageSize
    let hasMore = true

    while (hasMore) {
      const query: OrderListQuery = {
        page: currentPage,
        pageSize: pageSize,
        status: OrderStatus.UNSCHEDULED
      }

      const response = await orderApi.getOrderList(query)
      const pageOrders = response.orders || []
      allOrders = [...allOrders, ...pageOrders]

      // 检查是否还有更多数据
      hasMore = pageOrders.length === pageSize && currentPage < (response.totalPages || 1)
      currentPage++
    }

    orders.value = allOrders
    emit('orders-loaded', orders.value)

  } catch (error) {
    console.error('获取待分配订单失败:', error)
    message.error('获取待分配订单失败')
    orders.value = []
  } finally {
    loading.value = false
  }
}

// 搜索防抖
let searchTimeout: NodeJS.Timeout | null = null

// 事件处理
const handleSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    console.log('搜索订单:', searchKeyword.value)
    // 这里可以添加实际的搜索逻辑，比如调用API
  }, 300)
}

const handleFilter = () => {
  console.log('筛选订单类型:', filterType.value)
  // 筛选是即时的，不需要防抖
}

// 监听搜索关键词变化，自动触发搜索
watch(searchKeyword, () => {
  handleSearch()
})

const handleRefresh = () => {
  fetchUnassignedOrders()
}

// 拖拽事件处理
const handleDragStart = (event: DragEvent, order: Order) => {
  if (!event.dataTransfer) return

  isDragging.value = true
  draggedOrderId.value = order.id

  // 设置拖拽数据 - 使用多种格式确保兼容性
  const dragData = JSON.stringify(order)

  // 设置多种数据格式
  event.dataTransfer.setData('text/plain', dragData)  // 主要格式
  event.dataTransfer.setData('application/json', dragData)  // 备用格式
  event.dataTransfer.setData('text/x-order-data', dragData)  // 自定义格式
  event.dataTransfer.effectAllowed = 'move'

  // 使用currentTarget而不是target作为拖拽图像
  const dragElement = event.currentTarget as Element
  if (dragElement) {
    event.dataTransfer.setDragImage(dragElement, 50, 25)
  }

  emit('drag-start', order)
}

const handleDragEnd = () => {
  isDragging.value = false
  draggedOrderId.value = null
  emit('drag-end')
}

// 处理拖拽到待分配区域（取消分配）
const handleDragOverUnassigned = (event: DragEvent) => {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'move'
}

const handleDragEnterUnassigned = (event: DragEvent) => {
  event.preventDefault()
  isDragOverUnassigned.value = true
}

const handleDragLeaveUnassigned = (event: DragEvent) => {
  event.preventDefault()
  const currentTarget = event.currentTarget as Element
  const relatedTarget = event.relatedTarget as Node
  if (!currentTarget?.contains(relatedTarget)) {
    isDragOverUnassigned.value = false
  }
}

const handleDropToUnassigned = async (event: DragEvent) => {
  event.preventDefault()
  isDragOverUnassigned.value = false

  try {
    // 尝试多种数据格式
    let dragDataString = event.dataTransfer!.getData('text/plain')
    if (!dragDataString) {
      dragDataString = event.dataTransfer!.getData('application/json')
    }
    if (!dragDataString) {
      dragDataString = event.dataTransfer!.getData('text/x-sequence-data')
    }

    if (!dragDataString) {
      return
    }

    const dragData = JSON.parse(dragDataString)

    // 检查是否是排产记录（来自已分配列表）
    if (dragData && dragData.type === 'sequence' && dragData.sequence) {
      await removeSequence(dragData.sequence)
    }
  } catch (error) {
    console.error('处理拖拽数据失败:', error)
    message.error('取消分配失败')
  }
}

// 移除排产记录（取消分配）
const removeSequence = async (sequence: DeviceOrderSequence) => {
  try {
    await sequenceApi.deleteSequence(sequence.id)

    // 刷新待分配订单列表
    await fetchUnassignedOrders()

    emit('sequence-removed', sequence.id)
    message.success(`取消分配成功：${sequence.order?.code || sequence.orderId}`)
  } catch (error: any) {
    console.error('取消分配失败:', error)

    if (error?.response?.status === 403) {
      message.error('权限不足：当前用户没有删除生产计划的权限，请联系管理员')
    } else if (error?.response?.status === 409) {
      message.error('排产记录已在生产中，无法取消分配')
    } else if (error?.response?.status === 404) {
      message.error('排产记录不存在')
    } else {
      message.error('取消分配失败，请稍后重试')
    }
  }
}

// 暴露方法给父组件
const refreshOrders = () => {
  fetchUnassignedOrders()
}

// 生命周期
onMounted(() => {
  fetchUnassignedOrders()
})

// 暴露给父组件的方法
defineExpose({
  refreshOrders
})
</script>

<style scoped>
.unassigned-order-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-filter-section {
  margin-bottom: 12px;
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  flex: 1;
}

.filter-controls {
  flex-shrink: 0;
}

.order-list-container {
  flex: 1;
  overflow: hidden;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.order-list-container.drop-zone-active {
  background-color: #f6ffed;
  border: 2px dashed #52c41a;
}

.order-list {
  height: 100%;
  overflow-y: auto;
  padding-right: 4px;
}

.order-card {
  position: relative;
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fff;
  cursor: grab;
  transition: all 0.2s ease;
  user-select: none;
}

.order-card:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.order-card:active {
  cursor: grabbing;
}

.order-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.drag-handle {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #bfbfbf;
  font-size: 12px;
}

.order-info {
  width: 100%;
  padding-right: 20px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.order-code {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.code-text {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.order-type {
  font-size: 12px;
}

.order-actions {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.order-status {
  flex-shrink: 0;
}

.order-main-info {
  display: flex;
  gap: 16px;
  margin-bottom: 6px;
}

.info-left {
  flex: 1;
  min-width: 0;
}

.info-right {
  flex-shrink: 0;
  min-width: 120px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  flex-wrap: wrap;
  gap: 4px;
}

.info-separator {
  color: #d9d9d9;
  margin: 0 4px;
  font-size: 12px;
}

.order-remark {
  margin-top: 6px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-icon {
  color: #8c8c8c;
  font-size: 12px;
  width: 12px;
  flex-shrink: 0;
}

.info-text {
  color: #595959;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.delivery-date {
  color: #fa8c16;
  font-weight: 500;
}

.info-label {
  color: #8c8c8c;
  font-size: 12px;
  flex-shrink: 0;
}

.info-value {
  color: #595959;
  font-weight: 500;
  font-size: 12px;
  flex-shrink: 0;
}

.remark-text {
  font-style: italic;
  color: #8c8c8c;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.drag-indicator {
  position: absolute;
  top: 50%;
  right: -10px;
  transform: translateY(-50%);
  color: #1890ff;
  font-size: 16px;
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(-50%) translateX(0);
  }
  40% {
    transform: translateY(-50%) translateX(5px);
  }
  60% {
    transform: translateY(-50%) translateX(3px);
  }
}

.empty-state {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.3;
}

.order-stats {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  font-size: 12px;
  color: #8c8c8c;
}

/* 滚动条样式 */
.order-list::-webkit-scrollbar {
  width: 4px;
}

.order-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.order-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.order-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 拖拽状态样式 */
.order-card[draggable="true"]:hover .drag-handle {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-filter-section {
    flex-direction: column;
    gap: 8px;
  }

  .filter-controls {
    align-self: stretch;
    justify-content: space-between;
  }

  .order-card {
    padding: 10px;
  }

  .order-main-info {
    flex-direction: column;
    gap: 8px;
  }

  .info-right {
    min-width: auto;
  }

  .code-text {
    font-size: 13px;
  }

  .info-row {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .info-separator {
    display: none;
  }
}
</style>
