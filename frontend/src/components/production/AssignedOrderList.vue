<!--
  已分配订单列表组件 - 生产计划模块

  功能特性：
  - 显示设备分配的订单队列
  - 支持拖拽排序和移除
  - 生产状态管理（等待/生产中/已完成）
  - 生产进度跟踪
  - 响应式布局设计
  - 可访问性支持
  - 自动刷新机制

  {{CHENGQI: Action: Added; Timestamp: 2025-07-28 21:30:00 +08:00; Reason: Shrimp Task ID: #e8300ec6-64c5-46ff-ab75-acee95b4ef63, 创建已分配订单列表组件; Principle_Applied: 组件化设计、拖拽功能、API复用;}}
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 14:00:00 +08:00; Reason: Shrimp Task ID: #fa076766-4dc5-42e1-8100-77ad3fbeb5fa, 添加生产历史查看功能; Principle_Applied: 功能扩展;}}
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 14:25:00 +08:00; Reason: Shrimp Task ID: #d865b069-a97d-4c13-b507-52036e8c0b87, 优化界面交互添加生产状态操作; Principle_Applied: 用户体验优化;}}
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-29 XX:XX:XX +08:00; Reason: 优化布局和用户体验，添加响应式设计和可访问性支持; Principle_Applied: 用户体验优化、响应式设计、可访问性;}}
-->

<template>
  <div class="assigned-order-list">
    <!-- 设备信息显示 -->
    <div class="device-info" v-if="selectedDevice">
      <div class="device-header">
        <div class="device-name">
          <span class="name-text">{{ selectedDevice.name }}</span>
          <a-tag v-if="selectedDevice.code" size="small" color="blue">
            {{ selectedDevice.code }}
          </a-tag>
        </div>
        <div class="device-stats">
          <a-space size="small">
            <span class="stat-item">队列: {{ assignedSequences.length }}</span>
            <span class="stat-item">等待: {{ waitingCount }}</span>
            <span class="stat-item">生产: {{ productionCount }}</span>
          </a-space>
        </div>
      </div>
    </div>



    <!-- 拖拽接收区域 -->
    <div
      class="drop-zone"
      :class="{ 'drop-zone-active': isDragOver, 'drop-zone-empty': assignedSequences.length === 0 }"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
    >
      <a-spin :spinning="loading">
        <!-- 已分配订单列表 -->
        <div
          class="sequence-list"
          v-if="assignedSequences.length > 0"
        >
          <div
            v-for="(sequence, index) in assignedSequences"
            :key="sequence.id"
            class="sequence-item"
            :class="{ 
              'sequence-item-dragging': isDragging && draggedSequenceId === sequence.id,
              'sequence-item-production': sequence.status === DeviceOrderSequenceStatus.IN_PRODUCTION,
              'sequence-item-completed': sequence.status === DeviceOrderSequenceStatus.COMPLETED
            }"
            :draggable="canDragSequence(sequence)"
            @dragstart="handleSequenceDragStart($event, sequence, index)"
            @dragend="handleSequenceDragEnd"
          >
            <!-- 排序号 -->
            <div class="sequence-number">
              <a-tag 
                :color="getSequenceStatusColor(sequence.status)"
                class="sequence-tag"
              >
                {{ sequence.productionSequence || index + 1 }}
              </a-tag>
            </div>

            <!-- 订单信息 -->
            <div class="order-info">
              <!-- 订单头部 -->
              <div class="order-header">
                <div class="order-code">
                  <span class="code-text">{{ sequence.order?.code || `订单${sequence.orderId}` }}</span>
                  <a-tag
                    :color="getOrderTypeColor(sequence.order?.type)"
                    size="small"
                    class="order-type"
                  >
                    {{ getOrderTypeText(sequence.order?.type) }}
                  </a-tag>
                </div>
                <div class="sequence-status">
                  <a-tag
                    :color="getSequenceStatusColor(sequence.status)"
                    size="small"
                  >
                    {{ getSequenceStatusText(sequence.status) }}
                  </a-tag>
                </div>
              </div>

              <!-- 主要信息区域 -->
              <div class="order-main-info">
                <!-- 左侧信息 -->
                <div class="info-left">
                  <!-- 客户、销售和交期信息 -->
                  <div class="info-row" v-if="sequence.order">
                    <UserOutlined v-if="sequence.order.customerName" class="info-icon" />
                    <span v-if="sequence.order.customerName" class="info-text">{{ sequence.order.customerName }}</span>
                    <span v-if="sequence.order.customerName && sequence.order.salesman" class="info-separator">|</span>
                    <ContactsOutlined v-if="sequence.order.salesman" class="info-icon" />
                    <span v-if="sequence.order.salesman" class="info-text">{{ sequence.order.salesman }}</span>
                    <span v-if="(sequence.order.customerName || sequence.order.salesman) && sequence.order.deliveryDate" class="info-separator">|</span>
                    <ClockCircleOutlined v-if="sequence.order.deliveryDate" class="info-icon" />
                    <span v-if="sequence.order.deliveryDate" class="info-text delivery-date">交期: {{ formatDate(sequence.order.deliveryDate) }}</span>
                  </div>

                  <!-- 数量、车数和公式信息 -->
                  <div class="info-row">
                    <span v-if="sequence.productionQuantity" class="info-label">数量:</span>
                    <span v-if="sequence.productionQuantity" class="info-value">{{ sequence.productionQuantity }}</span>
                    <span v-if="sequence.productionQuantity && sequence.lathesNum" class="info-separator">|</span>
                    <span v-if="sequence.lathesNum" class="info-label">车数:</span>
                    <span v-if="sequence.lathesNum" class="info-value">{{ sequence.lathesNum }}</span>
                    <span v-if="(sequence.productionQuantity || sequence.lathesNum) && sequence.formula" class="info-separator">|</span>
                    <span v-if="sequence.formula" class="info-label">公式:</span>
                    <span v-if="sequence.formula" class="info-value">{{ sequence.formula }}</span>
                  </div>

                  <!-- 时间信息 -->
                  <div class="info-row" v-if="sequence.actualStartTime || sequence.actualEndTime">
                    <span v-if="sequence.actualStartTime" class="info-label">开始:</span>
                    <span v-if="sequence.actualStartTime" class="info-value time-value">{{ formatDateTime(sequence.actualStartTime, 'MM-DD HH:mm') }}</span>
                    <span v-if="sequence.actualStartTime && sequence.actualEndTime" class="info-separator">|</span>
                    <span v-if="sequence.actualEndTime" class="info-label">完成:</span>
                    <span v-if="sequence.actualEndTime" class="info-value time-value">{{ formatDateTime(sequence.actualEndTime, 'MM-DD HH:mm') }}</span>
                  </div>
                </div>

                <!-- 右侧信息 -->
                <div class="info-right">
                  <!-- 生产进度 -->
                  <div class="progress-container" v-if="sequence.status !== DeviceOrderSequenceStatus.WAITING">
                    <div class="progress-header">
                      <span class="info-label">进度:</span>
                      <span class="progress-text">{{ sequence.progress || 0 }}%</span>
                    </div>
                    <a-progress
                      :percent="sequence.progress || 0"
                      :stroke-color="getProgressColor(sequence.status, sequence.progress || 0)"
                      :show-info="false"
                      size="small"
                      class="progress-bar"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="sequence-actions">
              <!-- 主要操作按钮 -->
              <div class="action-row">
                <!-- 历史查看按钮 - 所有状态都可查看 -->
                <a-button
                  type="link"
                  size="small"
                  @click="handleViewHistory(sequence)"
                  title="查看历史"
                  :aria-label="`查看订单 ${sequence.order?.code || sequence.orderId} 的历史记录`"
                  class="action-btn"
                >
                  <HistoryOutlined />
                </a-button>

                <!-- 开始生产按钮 - 仅等待状态可开始 -->
                <a-button
                  v-if="sequence.status === DeviceOrderSequenceStatus.WAITING"
                  type="link"
                  size="small"
                  @click="handleStartProduction(sequence)"
                  title="开始生产"
                  :aria-label="`开始生产订单 ${sequence.order?.code || sequence.orderId}`"
                  :loading="sequence.id === operatingSequenceId"
                  class="action-btn"
                >
                  <PlayCircleOutlined />
                </a-button>

                <!-- 更新进度按钮 - 仅生产中状态可更新 -->
                <a-button
                  v-if="sequence.status === DeviceOrderSequenceStatus.IN_PRODUCTION"
                  type="link"
                  size="small"
                  @click="handleUpdateProgress(sequence)"
                  title="更新进度"
                  :loading="sequence.id === operatingSequenceId"
                  class="action-btn"
                >
                  <SyncOutlined />
                </a-button>

                <!-- 完成生产按钮 - 仅生产中状态可完成 -->
                <a-button
                  v-if="sequence.status === DeviceOrderSequenceStatus.IN_PRODUCTION"
                  type="link"
                  size="small"
                  @click="handleCompleteProduction(sequence)"
                  title="完成生产"
                  :loading="sequence.id === operatingSequenceId"
                  class="action-btn"
                >
                  <CheckCircleOutlined />
                </a-button>
              </div>

              <!-- 编辑操作按钮 - 仅等待状态可编辑 -->
              <div class="action-row" v-if="canEditSequence(sequence.status)">
                <a-button
                  type="link"
                  size="small"
                  @click="handleMoveUp(sequence, index)"
                  :disabled="index === 0"
                  title="上移"
                  class="action-btn"
                >
                  <UpOutlined />
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  @click="handleMoveDown(sequence, index)"
                  :disabled="index === assignedSequences.length - 1"
                  title="下移"
                  class="action-btn"
                >
                  <DownOutlined />
                </a-button>
                <a-button
                  type="link"
                  size="small"
                  danger
                  @click="handleRemoveSequence(sequence)"
                  title="移除"
                  class="action-btn"
                >
                  <DeleteOutlined />
                </a-button>
              </div>
            </div>

            <!-- 拖拽手柄 -->
            <div class="drag-handle" v-if="canDragSequence(sequence)">
              <DragOutlined />
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div
          v-else
          class="empty-state"
        >
          <a-empty description="暂无分配订单">
            <template #image>
              <InboxOutlined style="font-size: 48px; color: #d9d9d9;" />
            </template>
            <div class="empty-hint">
              拖拽右侧订单到此处进行分配
            </div>
          </a-empty>
        </div>
      </a-spin>
    </div>

    <!-- 生产历史查看弹窗 -->
    <ProductionHistoryModal
      ref="historyModalRef"
      :sequence-id="selectedSequenceId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, h, onBeforeUnmount } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  UserOutlined,
  ContactsOutlined,
  ClockCircleOutlined,
  UpOutlined,
  DownOutlined,
  DeleteOutlined,
  DragOutlined,
  InboxOutlined,
  HistoryOutlined,
  PlayCircleOutlined,
  SyncOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import { sequenceApi } from '../../api/order'
import type {
  DeviceOrderSequence,
  Order,
  CreateSequenceRequest
} from '../../types/order'
import {
  DeviceOrderSequenceStatus,
  OrderType,
  getSequenceStatusColor,
  getSequenceStatusLabel,
  getOrderTypeColor,
  getOrderTypeLabel,
  canEditSequence
} from '../../types/order'
import type { Device } from '../../types/device'
import { formatDateTime } from '../../utils/date'
import ProductionHistoryModal from './ProductionHistoryModal.vue'

// Props
interface Props {
  selectedDevice: Device | null
}

const props = defineProps<Props>()

// Emits
interface Emits {
  (e: 'sequence-created', sequence: DeviceOrderSequence): void
  (e: 'sequence-removed', sequenceId: number): void
  (e: 'sequences-reordered', deviceId: number): void
  (e: 'sequence-drag-start', sequence: DeviceOrderSequence): void
  (e: 'sequence-drag-end'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const assignedSequences = ref<DeviceOrderSequence[]>([])
const isDragOver = ref(false)
const isDragging = ref(false)
const draggedSequenceId = ref<number | null>(null)
const historyModalRef = ref()
const selectedSequenceId = ref<number | undefined>()
const operatingSequenceId = ref<number | null>(null)
const refreshTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const waitingCount = computed(() => {
  return assignedSequences.value.filter(seq => seq.status === DeviceOrderSequenceStatus.WAITING).length
})

const productionCount = computed(() => {
  return assignedSequences.value.filter(seq => seq.status === DeviceOrderSequenceStatus.IN_PRODUCTION).length
})

// 工具方法
const getSequenceStatusText = (status: DeviceOrderSequenceStatus): string => {
  return getSequenceStatusLabel(status)
}

const getOrderTypeText = (type: OrderType | undefined): string => {
  if (type === undefined) return '默认'
  return getOrderTypeLabel(type)
}

const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  return formatDateTime(dateString, 'YYYY-MM-DD')
}

const canDragSequence = (sequence: DeviceOrderSequence): boolean => {
  return canEditSequence(sequence.status)
}

const getProgressColor = (status: DeviceOrderSequenceStatus, progress: number): string => {
  if (status === DeviceOrderSequenceStatus.COMPLETED) {
    return '#52c41a' // 绿色 - 已完成
  }
  if (status === DeviceOrderSequenceStatus.IN_PRODUCTION) {
    if (progress >= 80) return '#52c41a' // 绿色 - 接近完成
    if (progress >= 50) return '#1890ff' // 蓝色 - 进行中
    if (progress >= 20) return '#faad14' // 黄色 - 刚开始
    return '#ff7875' // 红色 - 刚开始或有问题
  }
  return '#d9d9d9' // 灰色 - 等待中
}



// 数据获取
const fetchAssignedSequences = async (deviceId: number) => {
  if (!deviceId) {
    assignedSequences.value = []
    return
  }

  try {
    loading.value = true
    const sequences = await sequenceApi.getSequencesByDevice(deviceId)
    assignedSequences.value = sequences.sort((a, b) =>
      (a.productionSequence || 0) - (b.productionSequence || 0)
    )
  } catch (error) {
    console.error('获取已分配订单失败:', error)
    message.error('获取已分配订单失败')
    assignedSequences.value = []
  } finally {
    loading.value = false
  }
}

// 拖拽接收处理
const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = true
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  // 只有当离开整个drop-zone时才设置为false
  const currentTarget = event.currentTarget as Element
  const relatedTarget = event.relatedTarget as Node
  if (!currentTarget?.contains(relatedTarget)) {
    isDragOver.value = false
  }
}

const handleDrop = async (event: DragEvent) => {
  event.preventDefault()
  event.stopPropagation()
  isDragOver.value = false

  if (!props.selectedDevice) {
    message.error('请先选择设备')
    return
  }

  try {
    // 尝试多种数据格式
    let dragDataString = event.dataTransfer!.getData('text/plain')
    if (!dragDataString) {
      dragDataString = event.dataTransfer!.getData('application/json')
    }
    if (!dragDataString) {
      dragDataString = event.dataTransfer!.getData('text/x-order-data')
    }

    if (!dragDataString) {
      console.log('没有拖拽数据')
      return
    }

    const dragData = JSON.parse(dragDataString)
    console.log('解析后的拖拽数据:', dragData)

    // 检查拖拽数据类型
    if (dragData && dragData.type === 'sequence') {
      // 这是排产记录的内部拖拽，不在这里处理（由内部排序逻辑处理）
      console.log('内部排产记录拖拽，忽略')
    } else if (dragData && dragData.id && dragData.status !== undefined) {
      // 这是一个订单对象（来自待分配列表），创建排产记录
      await createSequenceFromOrder(dragData)
    } else {
      console.log('未识别的拖拽数据:', dragData)
      message.warning('无法识别的拖拽数据')
    }
  } catch (error) {
    console.error('处理拖拽数据失败:', error)
    message.error('分配订单失败')
  }
}

// 创建排产记录
const createSequenceFromOrder = async (order: Order) => {
  if (!props.selectedDevice) return

  // 显示加载状态
  loading.value = true

  try {
    // 检查订单是否有花样信息
    if (!order.patternInfo || order.patternInfo.length === 0) {
      message.error('订单缺少花样信息，无法分配。请先为订单添加花样信息。')
      return
    }

    // 检查设备是否在线（可选）
    if (!props.selectedDevice.ip) {
      message.warning('设备可能离线，但仍可以分配订单')
    }

    // 检查是否有多个花样，如果有则提示使用拆分功能
    if (order.patternInfo.length > 1) {
      message.warning(`该订单包含${order.patternInfo.length}个花样，建议使用"拆分"功能进行精确分配。当前将使用第一个花样进行分配。`)
    }

    // 解析花样ID
    const patternId = parseInt(order.patternInfo[0].patternId)
    if (isNaN(patternId)) {
      message.error('花样ID格式错误，无法分配')
      return
    }

    const createRequest: CreateSequenceRequest = {
      deviceId: props.selectedDevice.id,
      orderId: order.id,
      patternId: patternId, // 使用第一个花样
      formula: '1件=1头', // 默认公式
      lathesNum: 1 // 默认车数
    }

    const newSequence = await sequenceApi.createSequence(createRequest)

    // 重新获取排产列表以确保数据一致性
    await fetchAssignedSequences(props.selectedDevice.id)

    emit('sequence-created', newSequence)
    message.success(`订单 ${order.code || order.id} 分配成功`)
  } catch (error: any) {
    console.error('创建排产记录失败:', error)

    // 根据错误类型提供不同的错误信息
    if (error?.response?.status === 409) {
      message.error('订单已被分配到其他设备')
    } else if (error?.response?.status === 400) {
      message.error('订单信息不完整，无法分配')
    } else if (error?.message?.includes('network')) {
      message.error('网络连接失败，请检查网络后重试')
    } else {
      message.error('分配订单失败，请稍后重试')
    }
  } finally {
    loading.value = false
  }
}

// 内部拖拽排序处理
const handleSequenceDragStart = (event: DragEvent, sequence: DeviceOrderSequence, index: number) => {
  if (!canDragSequence(sequence)) {
    event.preventDefault()
    return
  }

  isDragging.value = true
  draggedSequenceId.value = sequence.id

  // 设置拖拽数据，标识这是内部排序
  const dragData = {
    type: 'sequence',
    sequence,
    index
  }

  const dragDataString = JSON.stringify(dragData)

  // 设置多种数据格式确保兼容性
  event.dataTransfer!.setData('text/plain', dragDataString)
  event.dataTransfer!.setData('application/json', dragDataString)
  event.dataTransfer!.setData('text/x-sequence-data', dragDataString)
  event.dataTransfer!.effectAllowed = 'move'

  // 通知父组件开始拖拽排产记录
  emit('sequence-drag-start', sequence)
}

const handleSequenceDragEnd = () => {
  isDragging.value = false
  draggedSequenceId.value = null

  // 通知父组件拖拽结束
  emit('sequence-drag-end')
}

// 排序操作（复用DeviceSchedulingModal的逻辑）
const handleMoveUp = async (sequence: DeviceOrderSequence, index: number) => {
  if (index === 0 || !props.selectedDevice) return

  // 保存原始状态用于回滚
  const originalSequences = [...assignedSequences.value]

  try {
    const currentSequence = assignedSequences.value[index]
    const prevSequence = assignedSequences.value[index - 1]

    // 交换排序号
    const tempSequence = currentSequence.productionSequence
    currentSequence.productionSequence = prevSequence.productionSequence
    prevSequence.productionSequence = tempSequence

    // 更新排序
    const sequenceIds = assignedSequences.value
      .sort((a, b) => (a.productionSequence || 0) - (b.productionSequence || 0))
      .map(item => item.id)

    await sequenceApi.reorderSequences({
      deviceId: props.selectedDevice.id,
      sequenceIds
    })

    message.success('调整排序成功')
    await fetchAssignedSequences(props.selectedDevice.id)
    emit('sequences-reordered', props.selectedDevice.id)
  } catch (error: any) {
    console.error('调整排序失败:', error)

    // 回滚到原始状态
    assignedSequences.value = originalSequences

    if (error?.response?.status === 409) {
      message.error('排序冲突，请刷新后重试')
    } else {
      message.error('调整排序失败，请稍后重试')
    }
  }
}

const handleMoveDown = async (sequence: DeviceOrderSequence, index: number) => {
  if (index === assignedSequences.value.length - 1 || !props.selectedDevice) return

  // 保存原始状态用于回滚
  const originalSequences = [...assignedSequences.value]

  try {
    const currentSequence = assignedSequences.value[index]
    const nextSequence = assignedSequences.value[index + 1]

    // 交换排序号
    const tempSequence = currentSequence.productionSequence
    currentSequence.productionSequence = nextSequence.productionSequence
    nextSequence.productionSequence = tempSequence

    // 更新排序
    const sequenceIds = assignedSequences.value
      .sort((a, b) => (a.productionSequence || 0) - (b.productionSequence || 0))
      .map(item => item.id)

    await sequenceApi.reorderSequences({
      deviceId: props.selectedDevice.id,
      sequenceIds
    })

    message.success('调整排序成功')
    await fetchAssignedSequences(props.selectedDevice.id)
    emit('sequences-reordered', props.selectedDevice.id)
  } catch (error: any) {
    console.error('调整排序失败:', error)

    // 回滚到原始状态
    assignedSequences.value = originalSequences

    if (error?.response?.status === 409) {
      message.error('排序冲突，请刷新后重试')
    } else {
      message.error('调整排序失败，请稍后重试')
    }
  }
}

// 移除排产记录
const handleRemoveSequence = async (sequence: DeviceOrderSequence) => {
  // 保存原始状态用于回滚
  const originalSequences = [...assignedSequences.value]

  try {
    // 乐观更新：先从列表中移除
    assignedSequences.value = assignedSequences.value.filter(seq => seq.id !== sequence.id)

    await sequenceApi.deleteSequence(sequence.id)

    emit('sequence-removed', sequence.id)
    message.success(`移除排产成功：${sequence.order?.code || sequence.orderId}`)
  } catch (error: any) {
    console.error('移除排产失败:', error)

    // 回滚到原始状态
    assignedSequences.value = originalSequences

    if (error?.response?.status === 409) {
      message.error('排产记录已在生产中，无法移除')
    } else if (error?.response?.status === 404) {
      message.error('排产记录不存在')
    } else {
      message.error('移除排产失败，请稍后重试')
    }
  }
}

// 查看生产历史
const handleViewHistory = (sequence: DeviceOrderSequence) => {
  selectedSequenceId.value = sequence.id
  historyModalRef.value?.show()
}

// 开始生产
const handleStartProduction = async (sequence: DeviceOrderSequence) => {
  try {
    operatingSequenceId.value = sequence.id

    await sequenceApi.startProduction(sequence.id, {
      remark: '开始生产'
    })

    message.success(`开始生产：${sequence.order?.code || sequence.orderId}`)

    // 刷新列表
    if (props.selectedDevice) {
      await fetchAssignedSequences(props.selectedDevice.id)
    }
  } catch (error: any) {
    console.error('开始生产失败:', error)
    message.error('开始生产失败，请稍后重试')
  } finally {
    operatingSequenceId.value = null
  }
}

// 完成生产
const handleCompleteProduction = async (sequence: DeviceOrderSequence) => {
  try {
    operatingSequenceId.value = sequence.id

    await sequenceApi.completeProduction(sequence.id, {
      actualQuantity: sequence.productionQuantity,
      remark: '完成生产'
    })

    message.success(`完成生产：${sequence.order?.code || sequence.orderId}`)

    // 刷新列表
    if (props.selectedDevice) {
      await fetchAssignedSequences(props.selectedDevice.id)
    }
  } catch (error: any) {
    console.error('完成生产失败:', error)
    message.error('完成生产失败，请稍后重试')
  } finally {
    operatingSequenceId.value = null
  }
}

// 更新进度
const handleUpdateProgress = (sequence: DeviceOrderSequence) => {
  // 显示进度更新对话框
  showProgressModal(sequence)
}

// 显示进度更新模态框
const showProgressModal = (sequence: DeviceOrderSequence) => {
  const currentProgress = sequence.progress || 0

  // 使用Ant Design的Modal.confirm来快速输入进度
  const modal = Modal.confirm({
    title: '更新生产进度',
    content: h('div', [
      h('p', `当前进度：${currentProgress}%`),
      h('div', { style: 'margin: 16px 0;' }, [
        h('label', { style: 'display: block; margin-bottom: 8px;' }, '新进度：'),
        h('input', {
          id: 'progress-input',
          type: 'number',
          min: 0,
          max: 100,
          value: currentProgress,
          style: 'width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px;'
        })
      ])
    ]),
    onOk: async () => {
      const input = document.getElementById('progress-input') as HTMLInputElement
      const newProgress = parseInt(input.value)

      if (isNaN(newProgress) || newProgress < 0 || newProgress > 100) {
        message.error('请输入0-100之间的有效进度值')
        return Promise.reject()
      }

      try {
        operatingSequenceId.value = sequence.id

        await sequenceApi.updateProgress(sequence.id, {
          progress: newProgress,
          remark: `进度更新：${currentProgress}% → ${newProgress}%`
        })

        message.success(`进度更新成功：${newProgress}%`)

        // 刷新列表
        if (props.selectedDevice) {
          await fetchAssignedSequences(props.selectedDevice.id)
        }
      } catch (error: any) {
        console.error('更新进度失败:', error)
        message.error('更新进度失败，请稍后重试')
        return Promise.reject()
      } finally {
        operatingSequenceId.value = null
      }
    }
  })
}

// 启动自动刷新
const startAutoRefresh = () => {
  stopAutoRefresh()
  if (props.selectedDevice) {
    refreshTimer.value = setInterval(() => {
      if (props.selectedDevice && !operatingSequenceId.value) {
        fetchAssignedSequences(props.selectedDevice.id)
      }
    }, 30000) // 每30秒刷新一次
  }
}

// 停止自动刷新
const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 监听设备变化
watch(() => props.selectedDevice, (newDevice) => {
  if (newDevice) {
    fetchAssignedSequences(newDevice.id)
    startAutoRefresh()
  } else {
    assignedSequences.value = []
    stopAutoRefresh()
  }
}, { immediate: true })



// 暴露方法给父组件
const refreshSequences = () => {
  if (props.selectedDevice) {
    fetchAssignedSequences(props.selectedDevice.id)
  }
}

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  stopAutoRefresh()
})

defineExpose({
  refreshSequences
})
</script>

<style scoped>
.assigned-order-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.device-info {
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.name-text {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.device-stats {
  font-size: 12px;
}

.stat-item {
  color: #8c8c8c;
}

.drop-zone {
  flex: 1;
  min-height: 200px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  transition: all 0.2s ease;
  position: relative;
  /* 确保可以接收拖拽事件 */
  pointer-events: auto;
  /* 移除overflow hidden，可能阻止拖拽事件 */
}

.drop-zone-active {
  border-color: #1890ff;
  background-color: #f6ffed;
}

.drop-zone-empty {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sequence-list {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
}

.sequence-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fff;
  transition: all 0.2s ease;
  cursor: grab;
}

.sequence-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

.sequence-item:active {
  cursor: grabbing;
}

.sequence-item-dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.sequence-item-production {
  border-color: #52c41a;
  background-color: #f6ffed;
}

.sequence-item-completed {
  border-color: #722ed1;
  background-color: #f9f0ff;
  opacity: 0.8;
}

.sequence-number {
  flex-shrink: 0;
  width: 40px;
  display: flex;
  justify-content: center;
}

.sequence-tag {
  font-weight: 600;
  font-size: 12px;
}

.order-info {
  flex: 1;
  min-width: 0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.order-code {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.code-text {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.order-type {
  font-size: 12px;
}

.sequence-status {
  flex-shrink: 0;
}

.order-main-info {
  display: flex;
  gap: 16px;
  margin-bottom: 6px;
}

.info-left {
  flex: 1;
  min-width: 0;
}

.info-right {
  flex-shrink: 0;
  min-width: 120px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  flex-wrap: wrap;
  gap: 4px;
}

.info-separator {
  color: #d9d9d9;
  margin: 0 4px;
  font-size: 12px;
}

.info-icon {
  color: #8c8c8c;
  font-size: 12px;
  width: 12px;
  flex-shrink: 0;
}

.info-text {
  color: #595959;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.delivery-date {
  color: #fa8c16;
  font-weight: 500;
}

.info-label {
  color: #8c8c8c;
  font-size: 12px;
  flex-shrink: 0;
}

.info-value {
  color: #595959;
  font-weight: 500;
  font-size: 12px;
  flex-shrink: 0;
}

.time-value {
  font-size: 11px;
  color: #8c8c8c;
}

.progress-container {
  margin-bottom: 8px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.progress-text {
  font-size: 12px;
  font-weight: 500;
  color: #1890ff;
}

.progress-bar {
  margin-bottom: 4px;
}

.sequence-actions {
  flex-shrink: 0;
  min-width: 80px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
  margin-right: 20px;
  margin-top: 4px;
}

.action-row {
  display: flex;
  gap: 2px;
  align-items: center;
}

.action-btn {
  padding: 2px 4px !important;
  height: 20px !important;
  min-width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn .anticon {
  font-size: 12px;
}

.drag-handle {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #bfbfbf;
  font-size: 12px;
}

.sequence-item:hover .drag-handle {
  color: #1890ff;
}

.empty-state {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-hint {
  margin-top: 8px;
  color: #8c8c8c;
  font-size: 12px;
}

/* 滚动条样式 */
.sequence-list::-webkit-scrollbar {
  width: 4px;
}

.sequence-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.sequence-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.sequence-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sequence-item {
    padding: 10px;
    gap: 8px;
  }

  .sequence-number {
    width: 30px;
  }

  .order-main-info {
    flex-direction: column;
    gap: 8px;
  }

  .info-right {
    min-width: auto;
  }

  .sequence-actions {
    min-width: 60px;
    margin-right: 15px;
  }

  .action-btn {
    min-width: 18px;
    height: 18px !important;
  }

  .code-text {
    font-size: 13px;
  }

  .info-row {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .sequence-item {
    flex-direction: column;
    align-items: stretch;
  }

  .sequence-number {
    align-self: flex-start;
    margin-bottom: 8px;
  }

  .sequence-actions {
    align-self: flex-end;
    margin-right: 0;
    margin-top: 8px;
  }

  .info-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .info-separator {
    display: none;
  }

  .drag-handle {
    top: 4px;
    right: 4px;
  }
}
</style>
