<!--
  生产历史记录查看组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-29 13:55:00 +08:00; Reason: Shrimp Task ID: #fa076766-4dc5-42e1-8100-77ad3fbeb5fa, 创建生产历史查看组件; Principle_Applied: 组件化设计;}}
-->
<template>
  <a-modal
    v-model:open="visible"
    title="生产历史记录"
    width="800px"
    :footer="null"
    @cancel="handleCancel"
  >
    <!-- 筛选条件 -->
    <div class="filter-section mb-4">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-select
            v-model:value="filters.actionType"
            placeholder="操作类型"
            allow-clear
            @change="handleFilterChange"
          >
            <a-select-option value="START">开始生产</a-select-option>
            <a-select-option value="PAUSE">暂停生产</a-select-option>
            <a-select-option value="RESUME">恢复生产</a-select-option>
            <a-select-option value="COMPLETE">完成生产</a-select-option>
            <a-select-option value="CANCEL">取消生产</a-select-option>
            <a-select-option value="ERROR">生产异常</a-select-option>
            <a-select-option value="PROGRESS_UPDATE">进度更新</a-select-option>
            <a-select-option value="QUANTITY_UPDATE">数量更新</a-select-option>
          </a-select>
        </a-col>
        <a-col :span="8">
          <a-date-picker
            v-model:value="filters.startDate"
            placeholder="开始日期"
            @change="handleFilterChange"
          />
        </a-col>
        <a-col :span="8">
          <a-date-picker
            v-model:value="filters.endDate"
            placeholder="结束日期"
            @change="handleFilterChange"
          />
        </a-col>
      </a-row>
    </div>

    <!-- 历史记录时间线 -->
    <div class="history-content">
      <a-spin :spinning="loading">
        <a-timeline v-if="historyList.length > 0">
          <a-timeline-item
            v-for="item in historyList"
            :key="item.id"
            :color="getTimelineColor(item.actionType)"
          >
            <template #dot>
              <component :is="getActionIcon(item.actionType)" />
            </template>
            <div class="history-item">
              <div class="history-header">
                <span class="action-type">{{ getActionTypeName(item.actionType) }}</span>
                <span class="action-time">{{ formatTime(item.actionTime) }}</span>
              </div>
              <div class="history-content">
                <div v-if="item.operator" class="operator">
                  操作员：{{ item.operator.realName || item.operator.username }}
                </div>
                <div v-if="item.previousValue || item.newValue" class="change-info">
                  {{ getChangeDescription(item) }}
                </div>
                <div v-if="item.remark" class="remark">
                  备注：{{ item.remark }}
                </div>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
        <a-empty v-else description="暂无历史记录" />
      </a-spin>
    </div>

    <!-- 分页 -->
    <div class="pagination-section mt-4" v-if="total > 0">
      <a-pagination
        v-model:current="pagination.page"
        v-model:page-size="pagination.pageSize"
        :total="total"
        :show-size-changer="true"
        :show-quick-jumper="true"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        @change="handlePageChange"
        @show-size-change="handlePageSizeChange"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed } from 'vue';
import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  EditOutlined
} from '@ant-design/icons-vue';
import { getProductionHistory } from '@/api/order';
import type { ProductionHistory } from '@/types/order';

interface Props {
  sequenceId?: number;
}

const props = withDefaults(defineProps<Props>(), {
  sequenceId: undefined
});

const emit = defineEmits<{
  cancel: [];
}>();

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const historyList = ref<ProductionHistory[]>([]);
const total = ref(0);

// 筛选条件
const filters = reactive({
  actionType: undefined as string | undefined,
  startDate: undefined as Dayjs | undefined,
  endDate: undefined as Dayjs | undefined
});

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 20
});

// 计算属性
const sequenceId = computed(() => props.sequenceId);

// 方法
const show = () => {
  visible.value = true;
  resetFilters();
  loadHistoryData();
};

const handleCancel = () => {
  visible.value = false;
  emit('cancel');
};

const resetFilters = () => {
  filters.actionType = undefined;
  filters.startDate = undefined;
  filters.endDate = undefined;
  pagination.page = 1;
  pagination.pageSize = 20;
};

const handleFilterChange = () => {
  pagination.page = 1;
  loadHistoryData();
};

const handlePageChange = (page: number) => {
  pagination.page = page;
  loadHistoryData();
};

const handlePageSizeChange = (current: number, size: number) => {
  pagination.page = 1;
  pagination.pageSize = size;
  loadHistoryData();
};

const loadHistoryData = async () => {
  try {
    loading.value = true;
    
    const params: any = {
      page: pagination.page,
      pageSize: pagination.pageSize
    };

    if (sequenceId.value) {
      params.sequenceId = sequenceId.value;
    }

    if (filters.actionType) {
      params.actionType = filters.actionType;
    }

    if (filters.startDate) {
      params.startDate = filters.startDate.format('YYYY-MM-DD');
    }

    if (filters.endDate) {
      params.endDate = filters.endDate.format('YYYY-MM-DD');
    }

    const response = await getProductionHistory(params);
    
    if (response.code === 200) {
      historyList.value = response.data.histories;
      total.value = response.data.total;
    } else {
      message.error(response.message || '获取历史记录失败');
    }
  } catch (error) {
    console.error('获取历史记录失败:', error);
    message.error('获取历史记录失败');
  } finally {
    loading.value = false;
  }
};

const getActionTypeName = (actionType: string): string => {
  const actionTypeNames: Record<string, string> = {
    START: '开始生产',
    PAUSE: '暂停生产',
    RESUME: '恢复生产',
    COMPLETE: '完成生产',
    CANCEL: '取消生产',
    ERROR: '生产异常',
    PROGRESS_UPDATE: '进度更新',
    QUANTITY_UPDATE: '数量更新'
  };
  return actionTypeNames[actionType] || '未知操作';
};

const getActionIcon = (actionType: string) => {
  const iconMap: Record<string, any> = {
    START: PlayCircleOutlined,
    PAUSE: PauseCircleOutlined,
    RESUME: PlayCircleOutlined,
    COMPLETE: CheckCircleOutlined,
    CANCEL: CloseCircleOutlined,
    ERROR: ExclamationCircleOutlined,
    PROGRESS_UPDATE: SyncOutlined,
    QUANTITY_UPDATE: EditOutlined
  };
  return iconMap[actionType] || SyncOutlined;
};

const getTimelineColor = (actionType: string): string => {
  const colorMap: Record<string, string> = {
    START: 'green',
    PAUSE: 'orange',
    RESUME: 'green',
    COMPLETE: 'blue',
    CANCEL: 'red',
    ERROR: 'red',
    PROGRESS_UPDATE: 'cyan',
    QUANTITY_UPDATE: 'purple'
  };
  return colorMap[actionType] || 'gray';
};

const getChangeDescription = (item: ProductionHistory): string => {
  if (item.previousValue && item.newValue) {
    return `从 ${item.previousValue} 变更为 ${item.newValue}`;
  }
  return item.remark || getActionTypeName(item.actionType);
};

const formatTime = (time: string): string => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
};

// 监听sequenceId变化
watch(sequenceId, () => {
  if (visible.value) {
    resetFilters();
    loadHistoryData();
  }
});

// 暴露方法
defineExpose({
  show
});
</script>

<style scoped>
.filter-section {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.history-content {
  max-height: 400px;
  overflow-y: auto;
}

.history-item {
  margin-bottom: 8px;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.action-type {
  font-weight: 500;
  color: #1890ff;
}

.action-time {
  font-size: 12px;
  color: #999;
}

.history-content .operator {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.history-content .change-info {
  font-size: 13px;
  color: #333;
  margin-bottom: 2px;
}

.history-content .remark {
  font-size: 12px;
  color: #999;
  font-style: italic;
}

.pagination-section {
  text-align: center;
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

:deep(.ant-timeline-item-content) {
  margin-left: 8px;
}

:deep(.ant-timeline-item-tail) {
  border-left: 2px solid #f0f0f0;
}
</style>
