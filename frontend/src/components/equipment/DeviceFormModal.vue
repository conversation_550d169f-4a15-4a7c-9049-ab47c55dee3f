<!--
  设备表单弹窗组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-23 15:30:28 +08:00; Reason: 设备管理页面开发, 创建设备新增编辑表单组件; Principle_Applied: 组件化设计;}}
-->

<template>
  <div class="device-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      @finish="handleSubmit"
    >
      <!-- 基本信息 -->
      <a-divider orientation="left">基本信息</a-divider>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="设备名称" name="name">
            <a-input
              v-model:value="formData.name"
              placeholder="请输入设备名称"
              :maxlength="100"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="设备编号" name="code">
            <a-input
              v-model:value="formData.code"
              placeholder="请输入设备编号"
              :maxlength="50"
            >
              <template #suffix>
                <a-button type="link" size="small" @click="generateCode">
                  生成
                </a-button>
              </template>
            </a-input>
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="设备SN" name="sn">
            <a-input
              v-model:value="formData.sn"
              placeholder="请输入设备SN"
              :maxlength="100"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="MAC地址" name="mac">
            <a-input
              v-model:value="formData.mac"
              placeholder="请输入MAC地址 (如: AA:BB:CC:DD:EE:FF)"
              :maxlength="17"
            />
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="IP地址" name="ip">
            <a-input
              v-model:value="formData.ip"
              placeholder="请输入IP地址"
              :maxlength="15"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="产线" name="productionLineId">
            <a-tree-select
              v-model:value="formData.productionLineId"
              placeholder="请选择产线"
              :tree-data="productionLineOptions"
              :loading="optionsLoading"
              tree-default-expand-all
              allow-clear
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 设备规格 -->
      <a-divider orientation="left">设备规格</a-divider>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="机型" name="deviceModelId">
            <a-select
              v-model:value="formData.deviceModelId"
              placeholder="请选择机型"
              :options="deviceModelOptions"
              :loading="optionsLoading"
              allow-clear
              show-search
              :filter-option="filterOption"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="厂商" name="vendor">
            <a-input
              v-model:value="formData.vendor"
              placeholder="请输入厂商"
              :maxlength="50"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="设备分组" name="groupId">
            <a-select
              v-model:value="formData.groupId"
              placeholder="请选择设备分组"
              :options="groupOptions"
              :loading="optionsLoading"
              allow-clear
              show-search
              :filter-option="filterOption"
            />
          </a-form-item>
        </a-col>

        <a-col :span="12">
          <!-- 预留位置 -->
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="电控型号" name="controlModel">
            <a-input
              v-model:value="formData.controlModel"
              placeholder="请输入电控型号"
              :maxlength="50"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="添加方式" name="registerWay">
            <a-select
              v-model:value="formData.registerWay"
              placeholder="请选择添加方式"
              :options="registerWayOptions"
            />
          </a-form-item>
        </a-col>
      </a-row>
      
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="机头头距(mm)" name="headSpace">
            <a-input-number
              v-model:value="formData.headSpace"
              placeholder="请输入机头头距"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="机头头数" name="headNum">
            <a-input-number
              v-model:value="formData.headNum"
              placeholder="请输入机头头数"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="机头针数" name="headNeedleNum">
            <a-input-number
              v-model:value="formData.headNeedleNum"
              placeholder="请输入机头针数"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 计算参数 -->
      <a-divider orientation="left">计算参数</a-divider>
      
      <a-row :gutter="16">
        <a-col :span="8">
          <a-form-item label="计算头距(mm)" name="formularHeadSpace">
            <a-input-number
              v-model:value="formData.formularHeadSpace"
              placeholder="请输入计算头距"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="计算头数" name="formularHeadNum">
            <a-input-number
              v-model:value="formData.formularHeadNum"
              placeholder="请输入计算头数"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="8">
          <a-form-item label="计算长度(mm)" name="formularLength">
            <a-input-number
              v-model:value="formData.formularLength"
              placeholder="请输入计算长度"
              :min="0"
              :precision="2"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 软件信息 -->
      <a-divider orientation="left">软件信息</a-divider>
      
      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="显示软件" name="displaySoftware">
            <a-input
              v-model:value="formData.displaySoftware"
              placeholder="请输入显示软件版本"
              :maxlength="100"
            />
          </a-form-item>
        </a-col>
        
        <a-col :span="12">
          <a-form-item label="主控软件" name="controlSoftware">
            <a-input
              v-model:value="formData.controlSoftware"
              placeholder="请输入主控软件版本"
              :maxlength="100"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 备注 -->
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注信息"
          :rows="3"
          :maxlength="500"
          show-count
        />
      </a-form-item>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <a-space>
          <a-button @click="handleCancel">
            取消
          </a-button>
          <a-button type="primary" html-type="submit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </a-button>
        </a-space>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'
import { deviceApi, deviceUtils } from '../../api/device'
import type { Device, CreateDeviceRequest, UpdateDeviceRequest, DeviceSearchOptions } from '../../types/device'

// Props
interface Props {
  device?: Device | null
  isEdit: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  success: []
  cancel: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const submitting = ref(false)
const optionsLoading = ref(false)

// 选项数据
const deviceModelOptions = ref<Array<{ label: string; value: number }>>([])
const productionLineOptions = ref<Array<{ title: string; value: number; key: number; children?: any[] }>>([])
const groupOptions = ref<Array<{ label: string; value: number }>>([])
const searchOptions = ref<DeviceSearchOptions>({
  deviceModels: [],
  productionLines: [],
  groups: []
})

// 表单数据
const formData = reactive<CreateDeviceRequest>({
  name: '',
  code: '',
  sn: '',
  mac: '',
  ip: undefined,
  remark: undefined,
  controlModel: undefined,
  deviceModelId: undefined,
  vendor: undefined,
  headSpace: undefined,
  headNum: undefined,
  headNeedleNum: undefined,
  formularHeadSpace: undefined,
  formularHeadNum: undefined,
  formularLength: undefined,
  displaySoftware: undefined,
  controlSoftware: undefined,
  productionLineId: undefined,
  groupId: undefined,
  registerWay: 'manual'
})

// 添加方式选项
const registerWayOptions = [
  { label: '手动添加', value: 'manual' },
  { label: '自动发现', value: 'auto' },
  { label: '扫描添加', value: 'scan' },
  { label: '批量导入', value: 'import' }
]

// 表单验证规则
const formRules: Record<string, Rule[]> = {
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { max: 100, message: '设备名称长度不能超过100个字符', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入设备编号', trigger: 'blur' },
    { max: 50, message: '设备编号长度不能超过50个字符', trigger: 'blur' }
  ],
  sn: [
    { required: true, message: '请输入设备SN', trigger: 'blur' },
    { max: 100, message: '设备SN长度不能超过100个字符', trigger: 'blur' }
  ],
  mac: [
    { required: true, message: '请输入MAC地址', trigger: 'blur' },
    {
      validator: (rule: any, value: string) => {
        if (value && !deviceUtils.isValidMacAddress(value)) {
          return Promise.reject('MAC地址格式不正确')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  ip: [
    {
      validator: (rule: any, value: string) => {
        if (value && !deviceUtils.isValidIpAddress(value)) {
          return Promise.reject('IP地址格式不正确')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  deviceModelId: [
    { type: 'number', message: '请选择机型', trigger: 'change' }
  ],
  vendor: [
    { max: 50, message: '厂商长度不能超过50个字符', trigger: 'blur' }
  ],
  controlModel: [
    { max: 50, message: '电控型号长度不能超过50个字符', trigger: 'blur' }
  ],
  headSpace: [
    { required: true, message: '请输入机头头距', trigger: 'blur' },
    { type: 'number', min: 0, message: '机头头距必须大于等于0', trigger: 'blur' }
  ],
  headNum: [
    { required: true, message: '请输入机头头数', trigger: 'blur' },
    { type: 'number', min: 0, message: '机头头数必须大于等于0', trigger: 'blur' }
  ],
  headNeedleNum: [
    { required: true, message: '请输入机头针数', trigger: 'blur' },
    { type: 'number', min: 0, message: '机头针数必须大于等于0', trigger: 'blur' }
  ],
  formularHeadSpace: [
    { required: true, message: '请输入计算头距', trigger: 'blur' },
    { type: 'number', min: 0, message: '计算头距必须大于等于0', trigger: 'blur' }
  ],
  formularHeadNum: [
    { required: true, message: '请输入计算头数', trigger: 'blur' },
    { type: 'number', min: 0, message: '计算头数必须大于等于0', trigger: 'blur' }
  ],
  formularLength: [
    { required: true, message: '请输入计算长度', trigger: 'blur' },
    { type: 'number', min: 0, message: '计算长度必须大于等于0', trigger: 'blur' }
  ],
  displaySoftware: [
    { max: 100, message: '显示软件长度不能超过100个字符', trigger: 'blur' }
  ],
  controlSoftware: [
    { max: 100, message: '主控软件长度不能超过100个字符', trigger: 'blur' }
  ],
  productionLineId: [
    { type: 'number', message: '请选择产线', trigger: 'change' }
  ],
  groupId: [
    { type: 'number', message: '请选择设备分组', trigger: 'change' }
  ],
  remark: [
    { max: 500, message: '备注长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 获取选项数据
const getSearchOptions = async () => {
  try {
    optionsLoading.value = true
    const options = await deviceApi.getSearchOptions()
    searchOptions.value = options

    // 设置机型选项
    deviceModelOptions.value = options.deviceModels.map(item => ({
      label: item.name,
      value: item.id
    }))

    // 设置产线选项（树形结构）
    const convertToTreeData = (nodes: any[]): any[] => {
      return nodes.map(node => ({
        title: node.title,
        value: node.value,
        key: node.key,
        children: node.children ? convertToTreeData(node.children) : undefined
      }))
    }
    productionLineOptions.value = convertToTreeData(options.productionLines)

    // 设置分组选项
    groupOptions.value = options.groups.map(item => ({
      label: item.name,
      value: item.id
    }))
  } catch (error) {
    console.error('获取选项数据失败:', error)
    message.error('获取选项数据失败，请稍后重试')
  } finally {
    optionsLoading.value = false
  }
}

// 搜索过滤方法
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 方法
const initFormData = () => {
  if (props.isEdit && props.device) {
    // 编辑模式，填充现有数据
    Object.assign(formData, {
      name: props.device.name,
      code: props.device.code || '',
      sn: props.device.sn || '',
      mac: props.device.mac || '',
      ip: props.device.ip || undefined,
      remark: props.device.remark || undefined,
      controlModel: props.device.controlModel || undefined,
      deviceModelId: props.device.deviceModelId,
      vendor: props.device.vendor || undefined,
      headSpace: props.device.headSpace,
      headNum: props.device.headNum,
      headNeedleNum: props.device.headNeedleNum,
      formularHeadSpace: props.device.formularHeadSpace,
      formularHeadNum: props.device.formularHeadNum,
      formularLength: props.device.formularLength,
      displaySoftware: props.device.displaySoftware || undefined,
      controlSoftware: props.device.controlSoftware || undefined,
      productionLineId: props.device.productionLineId,
      groupId: props.device.groupId,
      registerWay: props.device.registerWay || 'manual'
    })
  } else {
    // 新增模式，重置表单
    Object.assign(formData, {
      name: '',
      code: '',
      sn: '',
      mac: '',
      ip: undefined,
      remark: undefined,
      controlModel: undefined,
      deviceModelId: undefined,
      vendor: undefined,
      headSpace: undefined,
      headNum: undefined,
      headNeedleNum: undefined,
      formularHeadSpace: undefined,
      formularHeadNum: undefined,
      formularLength: undefined,
      displaySoftware: undefined,
      controlSoftware: undefined,
      productionLineId: undefined,
      groupId: undefined,
      registerWay: 'manual'
    })
  }
}

const generateCode = () => {
  formData.code = deviceUtils.generateDeviceCode('DEV')
}

// 清理表单数据，移除空字符串和undefined值
const cleanFormData = (data: any) => {
  const cleaned: any = {}
  for (const [key, value] of Object.entries(data)) {
    // 只保留有效值：非空字符串、非零数字、布尔值
    if (value !== '' && value !== undefined && value !== null) {
      cleaned[key] = value
    }
  }
  return cleaned
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true

    if (props.isEdit && props.device) {
      // 编辑设备
      const updateData: UpdateDeviceRequest = cleanFormData(formData)
      await deviceApi.updateDevice(props.device.id, updateData)
      message.success('更新设备成功')
    } else {
      // 创建设备
      const createData: CreateDeviceRequest = cleanFormData(formData)
      await deviceApi.createDevice(createData)
      message.success('创建设备成功')
    }

    emit('success')
  } catch (error) {
    console.error('提交表单失败:', error)
    if (error instanceof Error) {
      message.error(error.message || '操作失败，请稍后重试')
    } else {
      message.error('操作失败，请稍后重试')
    }
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听props变化
watch(() => [props.device, props.isEdit], () => {
  initFormData()
}, { immediate: true })

// 生命周期
onMounted(() => {
  getSearchOptions() // 获取选项数据
  initFormData()
})
</script>

<style scoped>
.device-form {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;
}

.form-actions {
  margin-top: 24px;
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .device-form {
    max-height: 60vh;
  }

  :deep(.ant-col) {
    margin-bottom: 8px;
  }
}
</style>
