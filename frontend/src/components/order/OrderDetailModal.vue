<!--
  订单详情弹窗组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-28 19:45:00 +08:00; Reason: Shrimp Task ID: #651e9047-0253-4986-8e8a-e76fd135d707, 创建订单详情组件; Principle_Applied: 组件化设计;}}
-->

<template>
  <a-modal
    title="订单详情"
    :open="visible"
    :width="1000"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="order-detail">
      <a-spin :spinning="loading">
        <!-- 订单基本信息 -->
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="订单编号" :span="2">
            <strong>{{ order?.code || '-' }}</strong>
            <a-tag v-if="order?.id" color="blue" style="margin-left: 8px">
              ID: {{ order.id }}
            </a-tag>
          </a-descriptions-item>
          
          <a-descriptions-item label="订单类型">
            <a-tag :color="getOrderTypeColor(order?.type)">
              {{ getOrderTypeLabel(order?.type) }}
            </a-tag>
          </a-descriptions-item>
          
          <a-descriptions-item label="订单状态">
            <a-tag :color="getOrderStatusColor(order?.status)">
              {{ getOrderStatusLabel(order?.status) }}
            </a-tag>
          </a-descriptions-item>
          
          <a-descriptions-item label="客户名称">
            {{ order?.customerName || '-' }}
          </a-descriptions-item>
          
          <a-descriptions-item label="业务员">
            {{ order?.salesman || '-' }}
          </a-descriptions-item>
          
          <a-descriptions-item label="下单日期">
            {{ formatDate(order?.orderDate) }}
          </a-descriptions-item>
          
          <a-descriptions-item label="交货日期">
            <div class="delivery-date">
              {{ formatDate(order?.deliveryDate) }}
              <div v-if="isOrderDelayed(order?.deliveryDate)" class="delay-info">
                <a-tag color="red" size="small">
                  延期 {{ calculateDelayDays(order?.deliveryDate) }} 天
                </a-tag>
              </div>
            </div>
          </a-descriptions-item>
          
          <a-descriptions-item label="订单量">
            {{ order?.orderQuantity || 0 }}
          </a-descriptions-item>
          
          <a-descriptions-item label="记量单位">
            {{ getUnitLabel(order?.orderUnitId) }}
          </a-descriptions-item>
          
          <a-descriptions-item label="创建时间">
            {{ formatDateTime(order?.createdAt) }}
          </a-descriptions-item>
          
          <a-descriptions-item label="更新时间">
            {{ formatDateTime(order?.updatedAt) }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 花样信息 -->
        <a-divider>花样信息</a-divider>
        
        <div v-if="order?.patternInfo && order.patternInfo.length > 0" class="pattern-section">
          <a-table
            :columns="patternColumns"
            :data-source="order.patternInfo"
            :pagination="false"
            size="small"
            row-key="patternId"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ index + 1 }}
              </template>
              <template v-else-if="column.key === 'patternName'">
                {{ getPatternName(record.patternId) }}
              </template>
              <template v-else-if="column.key === 'patternCode'">
                {{ getPatternCode(record.patternId) }}
              </template>
              <template v-else-if="column.key === 'quantity'">
                {{ record.patternQuantity }}
              </template>
            </template>
          </a-table>
          
          <div class="pattern-summary">
            <a-statistic
              title="花样总数"
              :value="order.patternInfo.length"
              suffix="个"
            />
            <a-statistic
              title="总数量"
              :value="calculateTotalQuantity(order.patternInfo)"
              suffix="件"
            />
          </div>
        </div>
        
        <a-empty v-else description="暂无花样信息" />

        <!-- 订单文件 -->
        <a-divider>订单文件</a-divider>
        
        <div v-if="order?.orderFiles && order.orderFiles.length > 0" class="files-section">
          <a-list
            :data-source="order.orderFiles"
            size="small"
          >
            <template #renderItem="{ item, index }">
              <a-list-item>
                <template #actions>
                  <a-button type="link" size="small" @click="previewFile(item)">
                    <EyeOutlined />
                    预览
                  </a-button>
                  <a-button type="link" size="small" @click="downloadFile(item)">
                    <DownloadOutlined />
                    下载
                  </a-button>
                </template>
                
                <a-list-item-meta>
                  <template #title>
                    <div class="file-info">
                      <FileOutlined style="margin-right: 8px;" />
                      {{ getFileName(item) }}
                    </div>
                  </template>
                  <template #description>
                    <div class="file-meta">
                      <span>文件类型: {{ getFileExtension(item) }}</span>
                      <span style="margin-left: 16px;">序号: {{ index + 1 }}</span>
                    </div>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </div>
        
        <a-empty v-else description="暂无订单文件" />

        <!-- 设备排产情况 -->
        <a-divider>设备排产情况</a-divider>
        
        <div v-if="sequences && sequences.length > 0" class="sequences-section">
          <a-table
            :columns="sequenceColumns"
            :data-source="sequences"
            :pagination="false"
            size="small"
            row-key="id"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'device'">
                {{ record.device?.name || '-' }}
                <div class="device-code">{{ record.device?.code || '-' }}</div>
              </template>
              <template v-else-if="column.key === 'pattern'">
                {{ record.pattern?.name || '-' }}
                <div class="pattern-code">{{ record.pattern?.code || '-' }}</div>
              </template>
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getSequenceStatusColor(record.status)">
                  {{ getSequenceStatusLabel(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'production'">
                <div class="production-info">
                  <div>车数: {{ record.lathesNum || 0 }}</div>
                  <div>数量: {{ record.productionQuantity || 0 }}</div>
                  <div>公式: {{ record.formula || '1件=1头' }}</div>
                </div>
              </template>
              <template v-else-if="column.key === 'sequence'">
                <a-tag color="blue">{{ record.productionSequence || 0 }}</a-tag>
              </template>
              <template v-else-if="column.key === 'actions'">
                <a-button type="link" size="small" @click="showSequenceDetail(record)">
                  <EyeOutlined />
                  详情
                </a-button>
              </template>
            </template>
          </a-table>
          
          <div class="sequence-summary">
            <a-statistic
              title="排产设备"
              :value="getUniqueDeviceCount(sequences)"
              suffix="台"
            />
            <a-statistic
              title="排产记录"
              :value="sequences.length"
              suffix="条"
            />
            <a-statistic
              title="总生产量"
              :value="getTotalProductionQuantity(sequences)"
              suffix="件"
            />
          </div>
        </div>
        
        <a-empty v-else description="暂无排产信息" />

        <!-- 备注信息 -->
        <a-divider v-if="order?.remark">备注信息</a-divider>
        
        <div v-if="order?.remark" class="remark-section">
          <a-typography-paragraph>
            {{ order.remark }}
          </a-typography-paragraph>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-space>
            <a-button type="primary" @click="handleEdit" :disabled="!canEditOrder(order?.status)">
              <EditOutlined />
              编辑订单
            </a-button>
            <a-button @click="handlePrint">
              <PrinterOutlined />
              打印
            </a-button>
            <a-button @click="handleExport">
              <ExportOutlined />
              导出
            </a-button>
            <a-button @click="handleSequenceManage">
              <ScheduleOutlined />
              排产管理
            </a-button>
          </a-space>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import type { TableColumnsType } from 'ant-design-vue';
import {
  EyeOutlined,
  DownloadOutlined,
  FileOutlined,
  EditOutlined,
  PrinterOutlined,
  ExportOutlined,
  ScheduleOutlined
} from '@ant-design/icons-vue';
import { orderApi, sequenceApi, orderFileApi, OrderUtils } from '@/api/order';
import { tagApi } from '@/api/tag';
import type { Order, DeviceOrderSequence, PatternInfo } from '@/types/order';
import {
  getOrderStatusLabel,
  getOrderStatusColor,
  getOrderTypeLabel,
  getOrderTypeColor,
  getSequenceStatusLabel,
  getSequenceStatusColor,
  canEditOrder,
  calculateDelayDays,
  isOrderDelayed,
  formatOrderDate as formatDate,
  formatOrderDateTime as formatDateTime
} from '@/types/order';
import { TagType, TagStatus } from '@/types/tag';

// Props
interface Props {
  visible: boolean;
  orderId?: number;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  orderId: undefined
});

// Emits
const emit = defineEmits<{
  cancel: [];
  edit: [order: Order];
  sequenceManage: [order: Order];
}>();

// 响应式数据
const loading = ref(false);
const order = ref<Order>();
const sequences = ref<DeviceOrderSequence[]>([]);

// 获取计量单位选项
const getUnitOptions = async () => {
  try {
    const response = await tagApi.getTagList({
      type: TagType.PATTERN_UNIT,
      status: TagStatus.ENABLED,
      page: 1,
      pageSize: 100
    });

    unitOptions.value = response.tags.map(item => ({
      label: item.name,
      value: item.id
    }));
  } catch (error) {
    console.error('获取计量单位选项失败:', error);
    // 使用默认选项
    unitOptions.value = [
      { label: '件', value: 1 },
      { label: '套', value: 2 },
      { label: '个', value: 3 },
      { label: '米', value: 4 },
      { label: '码', value: 5 }
    ];
  }
};

// 花样表格列定义
const patternColumns: TableColumnsType = [
  {
    title: '序号',
    key: 'index',
    width: 60,
    align: 'center'
  },
  {
    title: '花样名称',
    key: 'patternName',
    width: 150
  },
  {
    title: '花样编号',
    key: 'patternCode',
    width: 120
  },
  {
    title: '数量',
    key: 'quantity',
    width: 80,
    align: 'center'
  }
];

// 排产表格列定义
const sequenceColumns: TableColumnsType = [
  {
    title: '设备信息',
    key: 'device',
    width: 150
  },
  {
    title: '花样信息',
    key: 'pattern',
    width: 150
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '生产信息',
    key: 'production',
    width: 120
  },
  {
    title: '排序',
    key: 'sequence',
    width: 80,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    align: 'center'
  }
];

// 计量单位选项
const unitOptions = ref<Array<{ label: string; value: number }>>([]);

// 获取记量单位标签
const getUnitLabel = (unitId?: number): string => {
  if (!unitId) return '-';
  const unit = unitOptions.value.find(item => item.value === unitId);
  return unit?.label || '-';
};

// 获取花样名称
const getPatternName = (patternId: string): string => {
  // TODO: 从花样选项中获取名称
  return `花样-${patternId}`;
};

// 获取花样编号
const getPatternCode = (patternId: string): string => {
  // TODO: 从花样选项中获取编号
  return `P${patternId}`;
};

// 计算花样总数量
const calculateTotalQuantity = (patternInfo: any[]): number => {
  return patternInfo.reduce((sum, item) => sum + item.patternQuantity, 0);
};

// 获取文件名
const getFileName = (fileUrl: string): string => {
  return fileUrl.split('/').pop() || '未知文件';
};

// 获取文件扩展名
const getFileExtension = (fileUrl: string): string => {
  const filename = getFileName(fileUrl);
  const extension = filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
  return extension.toUpperCase() || 'UNKNOWN';
};

// 获取唯一设备数量
const getUniqueDeviceCount = (sequences: DeviceOrderSequence[]): number => {
  const deviceIds = new Set(sequences.map(seq => seq.deviceId));
  return deviceIds.size;
};

// 获取总生产数量
const getTotalProductionQuantity = (sequences: DeviceOrderSequence[]): number => {
  return sequences.reduce((sum, seq) => sum + (seq.productionQuantity || 0), 0);
};

// 获取订单详情
const getOrderDetail = async () => {
  if (!props.orderId) return;

  try {
    loading.value = true;
    order.value = await orderApi.getOrderById(props.orderId);
  } catch (error) {
    console.error('获取订单详情失败:', error);
    message.error('获取订单详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取排产信息
const getSequences = async () => {
  if (!props.orderId) return;

  try {
    sequences.value = await sequenceApi.getSequencesByOrder(props.orderId);
  } catch (error) {
    console.error('获取排产信息失败:', error);
    message.error('获取排产信息失败');
  }
};

// 预览文件
const previewFile = (fileUrl: string) => {
  const previewUrl = orderFileApi.getFilePreviewUrl(fileUrl);
  window.open(previewUrl, '_blank');
};

// 下载文件
const downloadFile = (fileUrl: string) => {
  const fileName = getFileName(fileUrl);
  orderFileApi.downloadOrderFile(fileUrl, fileName);
};

// 显示排产详情
const showSequenceDetail = (sequence: DeviceOrderSequence) => {
  // TODO: 实现排产详情显示
  message.info('排产详情功能将在后续版本中实现');
};

// 处理编辑
const handleEdit = () => {
  if (order.value) {
    emit('edit', order.value);
  }
};

// 处理打印
const handlePrint = () => {
  window.print();
};

// 处理导出
const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能将在后续版本中实现');
};

// 处理排产管理
const handleSequenceManage = () => {
  if (order.value) {
    emit('sequenceManage', order.value);
  }
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
};

// 生命周期
onMounted(() => {
  getUnitOptions(); // 获取计量单位选项
});

// 监听props变化
watch(() => [props.visible, props.orderId], () => {
  if (props.visible && props.orderId) {
    getOrderDetail();
    getSequences();
  }
}, { immediate: true });
</script>

<style scoped>
.order-detail {
  max-height: 80vh;
  overflow-y: auto;
  padding-right: 8px;
}

.delivery-date .delay-info {
  margin-top: 4px;
}

.pattern-section {
  margin-bottom: 16px;
}

.pattern-summary {
  display: flex;
  gap: 24px;
  margin-top: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.files-section {
  margin-bottom: 16px;
}

.file-info {
  display: flex;
  align-items: center;
}

.file-meta {
  font-size: 12px;
  color: #666;
}

.sequences-section {
  margin-bottom: 16px;
}

.device-code,
.pattern-code {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.production-info {
  font-size: 12px;
  line-height: 1.4;
}

.production-info > div {
  margin-bottom: 2px;
}

.sequence-summary {
  display: flex;
  gap: 24px;
  margin-top: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.remark-section {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
  margin-bottom: 16px;
}

.action-buttons {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: center;
}

/* 描述列表样式优化 */
:deep(.ant-descriptions-title) {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}

:deep(.ant-descriptions-item-content) {
  word-break: break-all;
}

/* 表格样式优化 */
:deep(.ant-table-small .ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-small .ant-table-tbody > tr > td) {
  padding: 8px;
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 12px;
  color: #666;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
  font-weight: 600;
}

/* 分割线样式 */
:deep(.ant-divider-horizontal.ant-divider-with-text) {
  margin: 24px 0 16px 0;
  font-weight: 600;
}

/* 列表样式 */
:deep(.ant-list-item) {
  padding: 12px 0;
}

:deep(.ant-list-item-meta-title) {
  font-size: 14px;
  margin-bottom: 4px;
}

:deep(.ant-list-item-meta-description) {
  font-size: 12px;
  color: #666;
}

/* 按钮样式 */
:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
  font-size: 12px;
}

/* 标签样式 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-detail {
    max-height: 70vh;
  }

  .pattern-summary,
  .sequence-summary {
    flex-direction: column;
    gap: 12px;
  }

  :deep(.ant-descriptions) {
    font-size: 13px;
  }

  :deep(.ant-table-small) {
    font-size: 12px;
  }

  .action-buttons :deep(.ant-space) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 滚动条样式 */
.order-detail::-webkit-scrollbar {
  width: 6px;
}

.order-detail::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.order-detail::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.order-detail::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 打印样式 */
@media print {
  .action-buttons {
    display: none;
  }

  .order-detail {
    max-height: none;
    overflow: visible;
  }

  :deep(.ant-modal-content) {
    box-shadow: none;
    border: 1px solid #000;
  }
}
</style>
