<!--
  订单拆分模态框组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-30 12:15:00 +08:00; Reason: Shrimp Task ID: #90c66278-071c-40e4-a960-5700a89fe882, 创建订单拆分模态框组件; Principle_Applied: 组件化设计;}}
-->

<template>
  <a-modal
    :open="visible"
    title="订单拆分"
    width="900px"
    :confirm-loading="submitting"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="order-split-modal">
      <a-spin :spinning="loading">
        <!-- 订单信息展示区 -->
        <a-card size="small" title="订单信息" class="order-info-card">
          <a-descriptions :column="2" size="small">
            <a-descriptions-item label="订单编号">
              <strong>{{ order?.code || '-' }}</strong>
            </a-descriptions-item>
            <a-descriptions-item label="客户名称">
              {{ order?.customerName || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="订单总量">
              {{ order?.orderQuantity || 0 }}
            </a-descriptions-item>
            <a-descriptions-item label="剩余数量">
              <a-tag :color="remainingQuantity > 0 ? 'green' : 'red'">
                {{ remainingQuantity }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 设备选择区域 -->
        <a-card size="small" title="选择设备" class="device-selection-card">
          <div class="device-selection">
            <a-select
              v-model:value="selectedDeviceIds"
              mode="multiple"
              placeholder="请选择要分配的设备"
              style="width: 100%"
              :options="deviceOptions"
              :loading="optionsLoading"
              show-search
              :filter-option="filterOption"
              @change="handleDeviceSelectionChange"
            >
              <template #maxTagPlaceholder="omittedValues">
                <span style="color: #999">+{{ omittedValues.length }}台设备</span>
              </template>
            </a-select>
          </div>
        </a-card>

        <!-- 设备分配表格 -->
        <a-card size="small" title="设备分配" class="allocation-card" v-if="allocations.length > 0">
          <template #extra>
            <a-space>
              <!-- 计算公式设置 -->
              <div class="formula-inline">
                <a-input-number
                  v-model:value="formulaPieces"
                  :min="1"
                  :precision="0"
                  placeholder="数量"
                  size="small"
                  style="width: 60px"
                  @change="handleFormulaChange"
                />
                <span class="formula-text">{{ orderUnitName }}=</span>
                <a-input-number
                  v-model:value="formulaHeads"
                  :min="1"
                  :precision="0"
                  placeholder="头数"
                  size="small"
                  style="width: 60px"
                  @change="handleFormulaChange"
                />
                <span class="formula-text">头</span>
              </div>
              <a-button size="small" @click="handleAverageAllocation" :disabled="allocations.length === 0">
                <CalculatorOutlined />
                平均分配
              </a-button>
              <a-button size="small" @click="handleClearAllocations" danger>
                <DeleteOutlined />
                清空分配
              </a-button>
            </a-space>
          </template>
          
          <a-table
            :columns="allocationColumns"
            :data-source="allocations"
            :pagination="false"
            size="small"
            row-key="key"
          >
            <!-- 设备显示列 -->
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'device'">
                <div class="device-display">
                  <span class="device-name">{{ getDeviceName(record.deviceId) }}</span>
                  <a-tag size="small" color="blue" v-if="getDeviceCode(record.deviceId)">
                    {{ getDeviceCode(record.deviceId) }}
                  </a-tag>
                  <a-tag size="small" color="green">
                    {{ getDeviceHeadNumSync(record.deviceId) }}头
                  </a-tag>
                </div>
              </template>

              <!-- 花样选择列 -->
              <template v-else-if="column.key === 'pattern'">
                <a-select
                  :value="record.patternId || null"
                  placeholder="请选择花样"
                  style="width: 100%"
                  :options="orderPatternOptions"
                  :loading="optionsLoading"
                  show-search
                  :filter-option="filterOption"
                  :not-found-content="orderPatternOptions.length === 0 ? '该订单暂无花样信息' : '未找到匹配的花样'"
                  @change="(value) => { record.patternId = value || null; handleCalculateQuantity(record); }"
                />
              </template>

              <!-- 车数输入列 -->
              <template v-else-if="column.key === 'lathesNum'">
                <a-input-number
                  v-model:value="record.lathesNum"
                  placeholder="车数"
                  :min="1"
                  :precision="0"
                  style="width: 100%"
                  @change="() => handleCalculateQuantity(record)"
                />
              </template>

              <!-- 生产数量列 -->
              <template v-else-if="column.key === 'productionQuantity'">
                <div v-if="!record.patternId" class="production-quantity-placeholder">
                  <span style="color: #999; font-style: italic;">请选择花样</span>
                </div>
                <a-input-number
                  v-else
                  v-model:value="record.productionQuantity"
                  :disabled="true"
                  style="width: 100%"
                />
              </template>

              <!-- 操作列 -->
              <template v-else-if="column.key === 'actions'">
                <span class="action-placeholder">-</span>
              </template>
            </template>
          </a-table>

          <!-- 分配汇总 -->
          <div class="allocation-summary">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-statistic
                  title="分配设备"
                  :value="getUniqueDeviceCount()"
                  suffix="台"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="分配总量"
                  :value="getTotalAllocationQuantity()"
                  suffix="件"
                />
              </a-col>
              <a-col :span="8">
                <a-statistic
                  title="剩余数量"
                  :value="remainingQuantity"
                  :value-style="{ color: remainingQuantity >= 0 ? '#3f8600' : '#cf1322' }"
                  suffix="件"
                />
              </a-col>
            </a-row>
          </div>
        </a-card>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import type { TableColumnsType } from 'ant-design-vue';
import {
  DeleteOutlined,
  CalculatorOutlined
} from '@ant-design/icons-vue';
import { orderApi, sequenceApi, SequenceUtils } from '../../api/order';
import { deviceApi } from '../../api/device';
import { tagApi } from '../../api/tag';
import { TagType, TagStatus } from '../../types/tag';
import type {
  Order,
  SplitAllocation,
  BatchCreateSequenceRequest
} from '../../types/order';

// Props
interface Props {
  visible: boolean;
  order?: Order;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  order: undefined
});

// Emits
const emit = defineEmits<{
  success: [];
  cancel: [];
}>();

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const optionsLoading = ref(false);
const allocations = ref<SplitAllocation[]>([]);
const selectedDeviceIds = ref<number[]>([]);
let allocationKeyCounter = 0;

// 计算公式相关
const formulaPieces = ref(1);
const formulaHeads = ref(1);
const orderUnitName = ref('件');

// 选项数据
const deviceOptions = ref<Array<{ label: string; value: number }>>([]);
const patternOptions = ref<Array<{ label: string; value: number }>>([]);
const orderPatternOptions = ref<Array<{ label: string; value: number }>>([]);

// 表格列定义
const allocationColumns: TableColumnsType<SplitAllocation> = [
  {
    title: '设备',
    key: 'device',
    width: 200
  },
  {
    title: '花样',
    key: 'pattern',
    width: 200
  },
  {
    title: '车数',
    key: 'lathesNum',
    width: 120
  },
  {
    title: '生产数量',
    key: 'productionQuantity',
    width: 120
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    align: 'center'
  }
];

// 计算剩余可分配数量
const remainingQuantity = computed(() => {
  if (!props.order) return 0;
  const allocated = getTotalAllocationQuantity();
  return (props.order.orderQuantity || 0) - allocated;
});

// 工具函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 获取设备名称
const getDeviceName = (deviceId: number): string => {
  const device = deviceOptions.value.find(opt => opt.value === deviceId);
  if (!device) return `设备${deviceId}`;

  // 从label中提取设备名称（去掉括号中的代码部分）
  const match = device.label.match(/^(.+?)\s*\(/);
  return match ? match[1] : device.label;
};

// 获取设备代码
const getDeviceCode = (deviceId: number): string => {
  const device = deviceOptions.value.find(opt => opt.value === deviceId);
  if (!device) return '';

  // 从label中提取括号中的代码部分
  const match = device.label.match(/\(([^)]+)\)$/);
  return match ? match[1] : '';
};

// 获取设备头数（需要从后端API获取完整设备信息）
const getDeviceHeadNum = async (deviceId: number): Promise<number> => {
  try {
    // 调用设备详情API获取完整设备信息
    const device = await deviceApi.getDeviceById(deviceId);
    return device.headNum || 1;
  } catch (error) {
    console.error('获取设备头数失败:', error);
    return 1;
  }
};

// 获取设备完整信息的响应式数据
const deviceDetails = ref<Map<number, { headNum: number }>>(new Map());

// 获取订单单位名称
const getOrderUnitName = async (orderUnitId?: number) => {
  if (!orderUnitId) {
    orderUnitName.value = '件';
    return;
  }

  try {
    const response = await tagApi.getTagList({
      type: TagType.PATTERN_UNIT,
      status: TagStatus.ENABLED,
      page: 1,
      pageSize: 100
    });

    const unit = response.tags.find(tag => tag.id === orderUnitId);
    orderUnitName.value = unit?.name || '件';
  } catch (error) {
    console.error('获取订单单位名称失败:', error);
    orderUnitName.value = '件';
  }
};

// 生成当前计算公式字符串
const getCurrentFormula = (): string => {
  return `${formulaPieces.value}${orderUnitName.value}=${formulaHeads.value}头`;
};

// 处理公式变化
const handleFormulaChange = () => {
  // 重新计算所有分配项的生产数量
  allocations.value.forEach(allocation => {
    if (allocation.deviceId > 0 && allocation.lathesNum > 0) {
      handleCalculateQuantity(allocation);
    }
  });
};

// 获取唯一设备数量
const getUniqueDeviceCount = (): number => {
  const deviceIds = new Set(allocations.value.map(item => item.deviceId).filter(id => id > 0));
  return deviceIds.size;
};

// 获取分配总数量
const getTotalAllocationQuantity = (): number => {
  return allocations.value.reduce((sum, item) => sum + (item.productionQuantity || 0), 0);
};

// 加载设备详细信息
const loadDeviceDetails = async (deviceIds: number[]) => {
  for (const deviceId of deviceIds) {
    if (!deviceDetails.value.has(deviceId)) {
      try {
        const headNum = await getDeviceHeadNum(deviceId);
        deviceDetails.value.set(deviceId, { headNum });
      } catch (error) {
        console.error(`加载设备${deviceId}详情失败:`, error);
        deviceDetails.value.set(deviceId, { headNum: 1 });
      }
    }
  }
};

// 处理设备选择变化
const handleDeviceSelectionChange = async (deviceIds: number[]) => {
  // 加载设备详细信息
  await loadDeviceDetails(deviceIds);

  // 创建新的分配列表
  const newAllocations: SplitAllocation[] = deviceIds.map(deviceId => {
    // 查找是否已存在该设备的分配
    const existingAllocation = allocations.value.find(item => item.deviceId === deviceId);

    if (existingAllocation) {
      // 如果已存在，保留原有数据
      return { ...existingAllocation };
    } else {
      // 如果不存在，创建新的分配项
      return {
        key: ++allocationKeyCounter,
        deviceId: deviceId,
        patternId: null, // 改为null，避免显示0
        lathesNum: 1,
        formula: getCurrentFormula(), // 使用当前统一公式
        productionQuantity: 0
      };
    }
  });

  // 更新分配列表
  allocations.value = newAllocations;

  // 为新添加的设备计算生产数量
  newAllocations.forEach(allocation => {
    if (allocation.deviceId > 0 && allocation.productionQuantity === 0) {
      handleCalculateQuantity(allocation);
    }
  });
};

// 清空所有分配
const handleClearAllocations = () => {
  selectedDeviceIds.value = [];
  allocations.value = [];
};

// 获取选项数据
const getOptions = async () => {
  if (optionsLoading.value || deviceOptions.value.length > 0) {
    return;
  }

  try {
    optionsLoading.value = true;
    const options = await orderApi.getSearchOptions();

    // 设置设备选项
    deviceOptions.value = options.devices.map(item => ({
      label: `${item.name} (${item.code || item.id})`,
      value: item.id
    }));

    // 设置花样选项（保留全部花样选项作为备用）
    patternOptions.value = options.patterns.map(item => ({
      label: `${item.name} (${item.code || item.id})`,
      value: item.id
    }));

    // 设置订单花样选项（从订单的花样信息中获取）
    generateOrderPatternOptions();
  } catch (error) {
    console.error('获取选项数据失败:', error);
    message.error('获取选项数据失败');
  } finally {
    optionsLoading.value = false;
  }
};

// 生成订单花样选项
const generateOrderPatternOptions = () => {
  if (!props.order?.patternInfo || props.order.patternInfo.length === 0) {
    orderPatternOptions.value = [];
    console.warn('订单缺少花样信息');
    return;
  }

  // 从订单的花样信息中生成选项
  orderPatternOptions.value = props.order.patternInfo.map(patternInfo => {
    // 尝试从全部花样选项中找到对应的花样信息
    const fullPatternInfo = patternOptions.value.find(p => p.value === parseInt(patternInfo.patternId));

    return {
      label: fullPatternInfo?.label || `花样${patternInfo.patternId} (数量: ${patternInfo.patternQuantity})`,
      value: parseInt(patternInfo.patternId)
    };
  });

  console.log('订单花样选项:', orderPatternOptions.value);
};

// 注意：添加和删除分配项的逻辑现在通过设备选择器来控制

// 设备变化处理
const handleDeviceChange = (record: SplitAllocation) => {
  // 重新计算生产数量
  handleCalculateQuantity(record);

  // 强制更新组件以刷新其他行的设备选项
  // 这里通过修改allocations数组来触发响应式更新
  const index = allocations.value.findIndex(item => item.key === record.key);
  if (index !== -1) {
    allocations.value[index] = { ...record };
  }
};

// 获取设备头数（同步版本，用于显示）
const getDeviceHeadNumSync = (deviceId: number): number => {
  const deviceDetail = deviceDetails.value.get(deviceId);
  return deviceDetail?.headNum || 1;
};

// 计算生产数量
const handleCalculateQuantity = async (record: SplitAllocation) => {
  // 检查是否选择了花样、设备和车数
  if (record.patternId && record.patternId > 0 && record.deviceId > 0 && record.lathesNum > 0) {
    try {
      // 获取设备头数
      const deviceHeadNum = getDeviceHeadNumSync(record.deviceId);

      // 计算逻辑：
      // 如果公式是 formulaPieces件 = formulaHeads头
      // 那么 1车(deviceHeadNum头) = (deviceHeadNum / formulaHeads) * formulaPieces 件
      // 总生产数量 = 车数 * 每车生产数量
      const productionPerLathe = (deviceHeadNum / formulaHeads.value) * formulaPieces.value;
      record.productionQuantity = Math.floor(record.lathesNum * productionPerLathe);

      console.log('计算生产数量:', {
        deviceId: record.deviceId,
        patternId: record.patternId,
        deviceHeadNum,
        lathesNum: record.lathesNum,
        formulaPieces: formulaPieces.value,
        formulaHeads: formulaHeads.value,
        productionPerLathe,
        totalProduction: record.productionQuantity
      });
    } catch (error) {
      console.error('计算生产数量失败:', error);
      record.productionQuantity = 0;
    }
  } else {
    // 如果没有选择花样、设备或车数，不设置生产数量（保持原值或0）
    record.productionQuantity = 0;
  }
};

// 平均分配
const handleAverageAllocation = async () => {
  if (allocations.value.length === 0 || !props.order?.orderQuantity) {
    message.warning('请先添加设备分配项');
    return;
  }

  // 检查是否所有分配项都已选择设备和花样
  const incompleteItems = allocations.value.filter(item => !item.deviceId || item.patternId == null);
  if (incompleteItems.length > 0) {
    message.warning('请先为所有分配项选择设备和花样');
    return;
  }

  const orderQuantity = props.order.orderQuantity;
  const allocationCount = allocations.value.length;

  // 计算平均分配的目标生产数量
  const averageQuantity = Math.floor(orderQuantity / allocationCount);
  const remainder = orderQuantity % allocationCount;

  // 为每个分配项计算合适的车数
  for (let i = 0; i < allocations.value.length; i++) {
    const allocation = allocations.value[i];
    const targetQuantity = averageQuantity + (i < remainder ? 1 : 0);

    // 根据目标生产数量反推车数
    const deviceHeadNum = getDeviceHeadNumSync(allocation.deviceId);

    // 计算逻辑：
    // 每车生产数量 = (设备头数 / 公式头数) * 公式件数
    // 需要车数 = 目标数量 / 每车生产数量
    const productionPerLathe = (deviceHeadNum / formulaHeads.value) * formulaPieces.value;
    const requiredLathes = Math.ceil(targetQuantity / productionPerLathe);
    allocation.lathesNum = Math.max(1, requiredLathes);

    console.log('平均分配计算:', {
      deviceId: allocation.deviceId,
      deviceHeadNum,
      targetQuantity,
      formulaPieces: formulaPieces.value,
      formulaHeads: formulaHeads.value,
      productionPerLathe,
      requiredLathes: allocation.lathesNum
    });

    // 重新计算生产数量
    await handleCalculateQuantity(allocation);
  }

  message.success(`平均分配完成，目标分配${orderQuantity}件到${allocationCount}个设备`);
};

// 验证分配数据
const validateAllocations = (): boolean => {
  // 检查是否有分配项
  if (allocations.value.length === 0) {
    message.error('请至少添加一个设备分配');
    return false;
  }

  // 验证全局计算公式
  const formula = getCurrentFormula();
  if (!SequenceUtils.validateFormula(formula, orderUnitName.value)) {
    message.error(`计算公式格式不正确，应为"数字${orderUnitName.value}=数字头"，如"1${orderUnitName.value}=1头"`);
    return false;
  }

  // 检查每个分配项的完整性
  for (let i = 0; i < allocations.value.length; i++) {
    const item = allocations.value[i];
    const index = i + 1;

    if (!item.deviceId || item.deviceId <= 0) {
      message.error(`第${index}行：请选择设备`);
      return false;
    }

    if (item.patternId == null || item.patternId <= 0) {
      if (orderPatternOptions.value.length === 0) {
        message.error(`第${index}行：该订单暂无花样信息，无法进行设备分配`);
      } else {
        message.error(`第${index}行：请选择花样`);
      }
      return false;
    }

    if (!item.lathesNum || item.lathesNum <= 0) {
      message.error(`第${index}行：车数必须大于0`);
      return false;
    }

    // 计算公式现在是全局统一的，不需要单独验证

    // 验证车数的合理性
    if (!SequenceUtils.validateLathesNum(item.lathesNum)) {
      message.error(`第${index}行：车数必须是正整数`);
      return false;
    }

    // 检查生产数量是否已计算
    if (item.productionQuantity <= 0) {
      message.error(`第${index}行：生产数量计算异常，请检查车数和计算公式`);
      return false;
    }
  }

  // 检查重复设备
  const deviceIds = allocations.value.map(item => item.deviceId);
  const duplicateDeviceId = deviceIds.find((id, index) => deviceIds.indexOf(id) !== index);
  if (duplicateDeviceId) {
    const deviceName = deviceOptions.value.find(opt => opt.value === duplicateDeviceId)?.label || `设备${duplicateDeviceId}`;
    message.error(`设备"${deviceName}"被重复选择，请选择不同的设备`);
    return false;
  }

  // 检查分配总量
  const totalQuantity = getTotalAllocationQuantity();
  if (totalQuantity === 0) {
    message.error('分配总量不能为0，请检查车数和计算公式');
    return false;
  }

  // 检查数量是否超限
  if (remainingQuantity.value < 0) {
    const orderQuantity = props.order?.orderQuantity || 0;
    message.error(`分配总量(${totalQuantity})超过订单总量(${orderQuantity})，请调整分配数量`);
    return false;
  }

  // 检查是否完全分配（可选警告）
  if (remainingQuantity.value > 0) {
    const orderQuantity = props.order?.orderQuantity || 0;
    console.warn(`订单还有${remainingQuantity.value}件未分配，总量${orderQuantity}件`);
  }

  return true;
};

// 提交处理
const handleSubmit = async () => {
  if (!props.order?.id) return;

  if (!validateAllocations()) {
    return;
  }

  try {
    submitting.value = true;

    const data: BatchCreateSequenceRequest = {
      orderId: props.order.id,
      allocations: allocations.value
        .filter(item => item.patternId != null && item.patternId > 0) // 额外过滤确保patternId有效
        .map(item => ({
          deviceId: item.deviceId,
          patternId: item.patternId!, // 使用非空断言，因为验证已确保patternId存在
          lathesNum: item.lathesNum,
          formula: getCurrentFormula() // 使用统一的公式
        }))
    };

    await sequenceApi.batchCreateSequences(data);
    message.success('订单拆分成功');
    emit('success');
  } catch (error) {
    console.error('订单拆分失败:', error);
    message.error('订单拆分失败');
  } finally {
    submitting.value = false;
  }
};

// 取消处理
const handleCancel = () => {
  emit('cancel');
};

// 初始化数据
const initData = async () => {
  // 重置分配列表和设备选择
  allocations.value = [];
  selectedDeviceIds.value = [];
  allocationKeyCounter = 0;

  // 重置公式
  formulaPieces.value = 1;
  formulaHeads.value = 1;

  // 获取订单单位名称
  if (props.order?.orderUnitId) {
    await getOrderUnitName(props.order.orderUnitId);
  } else {
    orderUnitName.value = '件';
  }

  // 重新生成订单花样选项（如果已经加载了全部花样选项）
  if (patternOptions.value.length > 0) {
    generateOrderPatternOptions();
  }
};

// 监听props变化
watch(() => [props.visible, props.order], () => {
  if (props.visible && props.order) {
    initData();
    getOptions();
  }
}, { immediate: true });
</script>

<style scoped>
.order-split-modal {
  max-height: 70vh;
  overflow-y: auto;
}

.order-info-card,
.device-selection-card,
.allocation-card {
  margin-bottom: 16px;
}

.device-selection {
  min-height: 32px;
}

.formula-inline {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 8px;
}

.formula-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.device-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.device-name {
  font-weight: 500;
  color: #262626;
}

.action-placeholder {
  color: #d9d9d9;
  font-size: 12px;
}

.allocation-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 描述列表样式 */
:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}

/* 表格样式优化 */
:deep(.ant-table-small .ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
  padding: 8px;
}

:deep(.ant-table-small .ant-table-tbody > tr > td) {
  padding: 8px;
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 12px;
  color: #666;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
  font-weight: 600;
}

/* 卡片样式优化 */
:deep(.ant-card-head) {
  padding: 8px 16px;
  min-height: 40px;
}

:deep(.ant-card-body) {
  padding: 12px 16px;
}

:deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 600;
}

/* 生产数量占位符样式 */
.production-quantity-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}

/* 生产数量占位符样式 */
.production-quantity-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
}
</style>
