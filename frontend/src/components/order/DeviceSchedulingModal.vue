<!--
  设备排产管理组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-28 20:00:00 +08:00; Reason: Shrimp Task ID: #c8c521ce-a714-46a3-91d4-049aff6586dc, 创建设备排产管理组件; Principle_Applied: 组件化设计;}}
-->

<template>
  <a-modal
    title="设备排产管理"
    :open="visible"
    :width="1200"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="device-scheduling">
      <a-spin :spinning="loading">
        <!-- 订单信息 -->
        <a-card size="small" title="订单信息" class="order-info-card">
          <a-descriptions :column="4" size="small">
            <a-descriptions-item label="订单编号">
              <strong>{{ order?.code || '-' }}</strong>
            </a-descriptions-item>
            <a-descriptions-item label="客户名称">
              {{ order?.customerName || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="订单状态">
              <a-tag :color="getOrderStatusColor(order?.status)">
                {{ getOrderStatusLabel(order?.status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="交货日期">
              {{ formatDate(order?.deliveryDate) }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>

        <!-- 排产操作区 -->
        <a-card size="small" title="排产操作" class="scheduling-actions-card">
          <a-form layout="inline" :model="schedulingForm" class="scheduling-form">
            <a-form-item label="选择设备">
              <a-select
                v-model:value="schedulingForm.deviceId"
                placeholder="请选择设备"
                style="width: 200px"
                :options="deviceOptions"
                :loading="optionsLoading"
                show-search
                :filter-option="filterOption"
                @change="handleDeviceChange"
              />
            </a-form-item>

            <a-form-item label="选择花样">
              <a-select
                v-model:value="schedulingForm.patternId"
                placeholder="请选择花样"
                style="width: 200px"
                :options="patternOptions"
                :loading="optionsLoading"
                show-search
                :filter-option="filterOption"
                @change="handlePatternChange"
              />
            </a-form-item>

            <a-form-item label="车数">
              <a-input-number
                v-model:value="schedulingForm.lathesNum"
                placeholder="请输入车数"
                :min="0"
                :precision="0"
                style="width: 120px"
                @change="handleCalculateQuantity"
              />
            </a-form-item>

            <a-form-item label="计算公式">
              <a-input
                v-model:value="schedulingForm.formula"
                placeholder="如: 1件=1头"
                style="width: 120px"
                @change="handleCalculateQuantity"
              />
            </a-form-item>

            <a-form-item label="生产数量">
              <a-input-number
                v-model:value="schedulingForm.productionQuantity"
                :disabled="true"
                style="width: 120px"
              />
            </a-form-item>

            <a-form-item>
              <a-space>
                <a-button 
                  type="primary" 
                  @click="handleAddScheduling"
                  :loading="submitting"
                  :disabled="!canAddScheduling"
                >
                  <PlusOutlined />
                  添加排产
                </a-button>
                <a-button @click="handleCalculateQuantity">
                  <CalculatorOutlined />
                  重新计算
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 设备排产列表 -->
        <a-card size="small" title="设备排产列表" class="scheduling-list-card">
          <template #extra>
            <a-space>
              <a-button @click="handleRefresh" :loading="loading">
                <ReloadOutlined />
                刷新
              </a-button>
              <a-button 
                @click="handleBatchDelete" 
                danger
                :disabled="selectedRowKeys.length === 0"
              >
                <DeleteOutlined />
                批量删除
              </a-button>
            </a-space>
          </template>

          <a-table
            :columns="columns"
            :data-source="schedulingList"
            :loading="loading"
            :pagination="false"
            size="small"
            row-key="id"
            :row-selection="rowSelection"
            :scroll="{ y: 400 }"
          >
            <!-- 设备信息列 -->
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'device'">
                <div class="device-info">
                  <div class="device-name">{{ record.device?.name || '-' }}</div>
                  <div class="device-code">{{ record.device?.code || '-' }}</div>
                  <div class="device-heads">头数: {{ record.device?.headNum || 0 }}</div>
                </div>
              </template>

              <!-- 花样信息列 -->
              <template v-else-if="column.key === 'pattern'">
                <div class="pattern-info">
                  <div class="pattern-name">{{ record.pattern?.name || '-' }}</div>
                  <div class="pattern-code">{{ record.pattern?.code || '-' }}</div>
                </div>
              </template>

              <!-- 生产信息列 -->
              <template v-else-if="column.key === 'production'">
                <div class="production-info">
                  <div>车数: {{ record.lathesNum || 0 }}</div>
                  <div>数量: {{ record.productionQuantity || 0 }}</div>
                  <div>公式: {{ record.formula || '1件=1头' }}</div>
                </div>
              </template>

              <!-- 排序列 -->
              <template v-else-if="column.key === 'sequence'">
                <div class="sequence-info">
                  <a-tag color="blue">{{ record.productionSequence || 0 }}</a-tag>
                  <div class="sequence-actions">
                    <a-button 
                      type="link" 
                      size="small" 
                      @click="handleMoveUp(record, index)"
                      :disabled="index === 0 || !canEditSequence(record.status)"
                    >
                      <UpOutlined />
                    </a-button>
                    <a-button 
                      type="link" 
                      size="small" 
                      @click="handleMoveDown(record, index)"
                      :disabled="index === schedulingList.length - 1 || !canEditSequence(record.status)"
                    >
                      <DownOutlined />
                    </a-button>
                  </div>
                </div>
              </template>

              <!-- 状态列 -->
              <template v-else-if="column.key === 'status'">
                <a-tag :color="getSequenceStatusColor(record.status)">
                  {{ getSequenceStatusLabel(record.status) }}
                </a-tag>
              </template>

              <!-- 操作列 -->
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button 
                    type="link" 
                    size="small" 
                    @click="showEditModal(record)"
                    :disabled="!canEditSequence(record.status)"
                  >
                    <EditOutlined />
                    编辑
                  </a-button>
                  <a-dropdown>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item 
                          key="start" 
                          @click="handleStartProduction(record)"
                          :disabled="!canStartProduction(record.status)"
                        >
                          <PlayCircleOutlined />
                          开始生产
                        </a-menu-item>
                        <a-menu-item 
                          key="complete" 
                          @click="handleCompleteProduction(record)"
                          :disabled="!canCompleteProduction(record.status)"
                        >
                          <CheckCircleOutlined />
                          完成生产
                        </a-menu-item>
                        <a-menu-divider />
                        <a-menu-item 
                          key="delete" 
                          @click="showDeleteConfirm(record)"
                          :disabled="!canDeleteSequence(record.status)"
                          danger
                        >
                          <DeleteOutlined />
                          删除
                        </a-menu-item>
                      </a-menu>
                    </template>
                    <a-button type="link" size="small">
                      更多
                      <DownOutlined />
                    </a-button>
                  </a-dropdown>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 排产统计 -->
        <a-card size="small" title="排产统计" class="scheduling-stats-card">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic
                title="排产设备"
                :value="getUniqueDeviceCount(schedulingList)"
                suffix="台"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="排产记录"
                :value="schedulingList.length"
                suffix="条"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="总生产量"
                :value="getTotalProductionQuantity(schedulingList)"
                suffix="件"
              />
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="预计时长"
                :value="getEstimatedTime(schedulingList)"
                suffix="小时"
              />
            </a-col>
          </a-row>
        </a-card>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <a-space>
            <a-button @click="handleCancel">
              取消
            </a-button>
            <a-button type="primary" @click="handleSave">
              保存排产
            </a-button>
          </a-space>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import type { TableColumnsType } from 'ant-design-vue';
import {
  PlusOutlined,
  CalculatorOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EditOutlined,
  UpOutlined,
  DownOutlined,
  PlayCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue';
import { orderApi, sequenceApi, SequenceUtils } from '@/api/order';
import type { 
  Order, 
  DeviceOrderSequence, 
  CreateSequenceRequest,
  UpdateSequenceRequest,
  DeviceOrderSequenceStatus
} from '@/types/order';
import {
  getOrderStatusLabel,
  getOrderStatusColor,
  getSequenceStatusLabel,
  getSequenceStatusColor,
  canEditSequence,
  canDeleteSequence,
  canStartProduction,
  canCompleteProduction,
  formatOrderDate as formatDate
} from '@/types/order';

// Props
interface Props {
  visible: boolean;
  order?: Order;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  order: undefined
});

// Emits
const emit = defineEmits<{
  success: [];
  cancel: [];
}>();

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const optionsLoading = ref(false);
const schedulingList = ref<DeviceOrderSequence[]>([]);
const selectedRowKeys = ref<number[]>([]);

// 排产表单
const schedulingForm = reactive<CreateSequenceRequest>({
  deviceId: 0,
  orderId: 0,
  patternId: 0,
  formula: '1件=1头',
  lathesNum: 0,
  productionSequence: 1,
  productionQuantity: 0
});

// 选项数据
const deviceOptions = ref<Array<{ label: string; value: number }>>([]);
const patternOptions = ref<Array<{ label: string; value: number }>>([]);

// 表格列定义
const columns: TableColumnsType<DeviceOrderSequence> = [
  {
    title: '设备信息',
    key: 'device',
    width: 150
  },
  {
    title: '花样信息',
    key: 'pattern',
    width: 150
  },
  {
    title: '生产信息',
    key: 'production',
    width: 150
  },
  {
    title: '排序',
    key: 'sequence',
    width: 100,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    align: 'center'
  }
];

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: (record: DeviceOrderSequence) => ({
    disabled: !canDeleteSequence(record.status)
  })
}));

// 是否可以添加排产
const canAddScheduling = computed(() => {
  return schedulingForm.deviceId > 0 &&
         schedulingForm.patternId > 0 &&
         schedulingForm.lathesNum > 0 &&
         SequenceUtils.validateFormula(schedulingForm.formula);
});

// 工具函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 获取唯一设备数量
const getUniqueDeviceCount = (sequences: DeviceOrderSequence[]): number => {
  const deviceIds = new Set(sequences.map(seq => seq.deviceId));
  return deviceIds.size;
};

// 获取总生产数量
const getTotalProductionQuantity = (sequences: DeviceOrderSequence[]): number => {
  return sequences.reduce((sum, seq) => sum + (seq.productionQuantity || 0), 0);
};

// 获取预计时长
const getEstimatedTime = (sequences: DeviceOrderSequence[]): number => {
  const totalMinutes = SequenceUtils.estimateProductionTime(sequences);
  return Math.round(totalMinutes / 60 * 10) / 10; // 转换为小时，保留1位小数
};

// 获取选项数据
const getOptions = async () => {
  // 防止重复调用
  if (optionsLoading.value || deviceOptions.value.length > 0) {
    return;
  }

  try {
    optionsLoading.value = true;
    const options = await orderApi.getSearchOptions();

    // 设置设备选项
    deviceOptions.value = options.devices.map(item => ({
      label: `${item.name} (${item.code || item.id})`,
      value: item.id
    }));

    // 设置花样选项
    patternOptions.value = options.patterns.map(item => ({
      label: `${item.name} (${item.code || item.id})`,
      value: item.id
    }));
  } catch (error) {
    console.error('获取选项数据失败:', error);
  } finally {
    optionsLoading.value = false;
  }
};

// 获取排产列表
const getSchedulingList = async () => {
  if (!props.order?.id) return;

  try {
    loading.value = true;
    schedulingList.value = await sequenceApi.getSequencesByOrder(props.order.id);
  } catch (error) {
    console.error('获取排产列表失败:', error);
    message.error('获取排产列表失败');
  } finally {
    loading.value = false;
  }
};

// 设备变化处理
const handleDeviceChange = () => {
  handleCalculateQuantity();
};

// 花样变化处理
const handlePatternChange = () => {
  // 可以在这里添加花样相关的逻辑
};

// 计算生产数量
const handleCalculateQuantity = async () => {
  if (schedulingForm.deviceId > 0 && schedulingForm.lathesNum > 0 && schedulingForm.formula) {
    try {
      const result = await sequenceApi.calculateProductionQuantity({
        lathesNum: schedulingForm.lathesNum,
        deviceId: schedulingForm.deviceId,
        formula: schedulingForm.formula
      });
      schedulingForm.productionQuantity = result.productionQuantity;
    } catch (error) {
      console.error('计算生产数量失败:', error);
      // 使用本地计算作为备选
      const deviceOption = deviceOptions.value.find(opt => opt.value === schedulingForm.deviceId);
      if (deviceOption) {
        // 假设设备头数为1（实际应该从设备信息中获取）
        schedulingForm.productionQuantity = SequenceUtils.calculateProductionQuantity(
          schedulingForm.lathesNum,
          1,
          schedulingForm.formula
        );
      }
    }
  } else {
    schedulingForm.productionQuantity = 0;
  }
};

// 添加排产
const handleAddScheduling = async () => {
  if (!props.order?.id) return;

  try {
    submitting.value = true;

    const data: CreateSequenceRequest = {
      ...schedulingForm,
      orderId: props.order.id
    };

    await sequenceApi.createSequence(data);
    message.success('添加排产成功');

    // 重新获取排产列表
    await getSchedulingList();

    // 重置表单
    Object.assign(schedulingForm, {
      deviceId: 0,
      patternId: 0,
      formula: '1件=1头',
      lathesNum: 0,
      productionQuantity: 0
    });
  } catch (error) {
    console.error('添加排产失败:', error);
    message.error('添加排产失败');
  } finally {
    submitting.value = false;
  }
};

// 刷新数据
const handleRefresh = () => {
  getSchedulingList();
};

// 上移排产
const handleMoveUp = async (record: DeviceOrderSequence, index: number) => {
  if (index === 0) return;

  try {
    const currentSequence = schedulingList.value[index];
    const prevSequence = schedulingList.value[index - 1];

    // 交换排序号
    const tempSequence = currentSequence.productionSequence;
    currentSequence.productionSequence = prevSequence.productionSequence;
    prevSequence.productionSequence = tempSequence;

    // 更新排序
    const sequenceIds = schedulingList.value
      .sort((a, b) => (a.productionSequence || 0) - (b.productionSequence || 0))
      .map(item => item.id);

    await sequenceApi.reorderSequences({
      deviceId: record.deviceId,
      sequenceIds
    });

    message.success('调整排序成功');
    await getSchedulingList();
  } catch (error) {
    console.error('调整排序失败:', error);
    message.error('调整排序失败');
  }
};

// 下移排产
const handleMoveDown = async (record: DeviceOrderSequence, index: number) => {
  if (index === schedulingList.value.length - 1) return;

  try {
    const currentSequence = schedulingList.value[index];
    const nextSequence = schedulingList.value[index + 1];

    // 交换排序号
    const tempSequence = currentSequence.productionSequence;
    currentSequence.productionSequence = nextSequence.productionSequence;
    nextSequence.productionSequence = tempSequence;

    // 更新排序
    const sequenceIds = schedulingList.value
      .sort((a, b) => (a.productionSequence || 0) - (b.productionSequence || 0))
      .map(item => item.id);

    await sequenceApi.reorderSequences({
      deviceId: record.deviceId,
      sequenceIds
    });

    message.success('调整排序成功');
    await getSchedulingList();
  } catch (error) {
    console.error('调整排序失败:', error);
    message.error('调整排序失败');
  }
};

// 显示编辑模态框
const showEditModal = (record: DeviceOrderSequence) => {
  // TODO: 实现编辑排产模态框
  message.info('编辑排产功能将在后续版本中实现');
};

// 开始生产
const handleStartProduction = async (record: DeviceOrderSequence) => {
  try {
    await sequenceApi.updateSequence(record.id, { status: 1 }); // DeviceOrderSequenceStatus.IN_PRODUCTION
    message.success('开始生产成功');
    await getSchedulingList();
  } catch (error) {
    console.error('开始生产失败:', error);
    message.error('开始生产失败');
  }
};

// 完成生产
const handleCompleteProduction = async (record: DeviceOrderSequence) => {
  try {
    await sequenceApi.updateSequence(record.id, { status: 2 }); // DeviceOrderSequenceStatus.COMPLETED
    message.success('完成生产成功');
    await getSchedulingList();
  } catch (error) {
    console.error('完成生产失败:', error);
    message.error('完成生产失败');
  }
};

// 显示删除确认
const showDeleteConfirm = (record: DeviceOrderSequence) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除这条排产记录吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => handleDeleteSequence(record.id)
  });
};

// 删除排产
const handleDeleteSequence = async (id: number) => {
  try {
    await sequenceApi.deleteSequence(id);
    message.success('删除成功');
    await getSchedulingList();
  } catch (error) {
    console.error('删除排产失败:', error);
    message.error('删除失败');
  }
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的排产记录');
    return;
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条排产记录吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await sequenceApi.batchDeleteSequences(selectedRowKeys.value);
        message.success('批量删除成功');
        selectedRowKeys.value = [];
        await getSchedulingList();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    }
  });
};

// 保存排产
const handleSave = () => {
  emit('success');
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
};

// 初始化数据
const initData = () => {
  if (props.order?.id) {
    schedulingForm.orderId = props.order.id;
    getSchedulingList();
  }
};

// 监听props变化
watch(() => [props.visible, props.order], () => {
  if (props.visible && props.order) {
    initData();
    // 只有在模态框显示时才获取选项数据
    getOptions();
  }
}, { immediate: true });

// 生命周期 - 移除onMounted中的getOptions调用
</script>

<style scoped>
.device-scheduling {
  max-height: 80vh;
  overflow-y: auto;
  padding-right: 8px;
}

.order-info-card,
.scheduling-actions-card,
.scheduling-list-card,
.scheduling-stats-card {
  margin-bottom: 16px;
}

.scheduling-form {
  margin-bottom: 0;
}

.scheduling-form :deep(.ant-form-item) {
  margin-bottom: 8px;
}

.device-info,
.pattern-info {
  font-size: 12px;
  line-height: 1.4;
}

.device-name,
.pattern-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.device-code,
.pattern-code,
.device-heads {
  color: #666;
  font-size: 11px;
}

.production-info {
  font-size: 12px;
  line-height: 1.4;
}

.production-info > div {
  margin-bottom: 2px;
}

.sequence-info {
  text-align: center;
}

.sequence-actions {
  margin-top: 4px;
  display: flex;
  justify-content: center;
  gap: 4px;
}

.action-buttons {
  margin-top: 16px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

/* 卡片样式优化 */
:deep(.ant-card-head) {
  padding: 8px 16px;
  min-height: 40px;
}

:deep(.ant-card-body) {
  padding: 12px 16px;
}

:deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 600;
}

/* 描述列表样式 */
:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}

/* 表格样式优化 */
:deep(.ant-table-small .ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
  padding: 8px;
}

:deep(.ant-table-small .ant-table-tbody > tr > td) {
  padding: 8px;
}

/* 统计卡片样式 */
:deep(.ant-statistic-title) {
  font-size: 12px;
  color: #666;
}

:deep(.ant-statistic-content) {
  font-size: 16px;
  font-weight: 600;
}

/* 按钮样式 */
:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
  font-size: 12px;
}

/* 表单样式 */
:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-input-number) {
  border-radius: 6px;
}

/* 标签样式 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .device-scheduling {
    max-height: 70vh;
  }

  .scheduling-form :deep(.ant-form-item) {
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .device-scheduling {
    max-height: 60vh;
  }

  .scheduling-form :deep(.ant-form-item) {
    width: 100%;
    margin-bottom: 8px;
  }

  .scheduling-form :deep(.ant-input),
  .scheduling-form :deep(.ant-select),
  .scheduling-form :deep(.ant-input-number) {
    width: 100% !important;
  }

  :deep(.ant-table-small) {
    font-size: 11px;
  }

  .action-buttons :deep(.ant-space) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 滚动条样式 */
.device-scheduling::-webkit-scrollbar {
  width: 6px;
}

.device-scheduling::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.device-scheduling::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.device-scheduling::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格滚动区域样式 */
:deep(.ant-table-body) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

:deep(.ant-table-body::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.ant-table-body::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 3px;
}

:deep(.ant-table-body::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 3px;
}

/* 空状态样式 */
:deep(.ant-empty) {
  margin: 20px 0;
}

/* 加载状态样式 */
:deep(.ant-spin-container) {
  min-height: 200px;
}
</style>
