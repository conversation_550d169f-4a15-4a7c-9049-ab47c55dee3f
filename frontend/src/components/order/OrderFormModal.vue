<!--
  订单表单弹窗组件
  {{CHENGQI: Action: Added; Timestamp: 2025-07-28 19:30:00 +08:00; Reason: Shrimp Task ID: #5b9e1f1f-2b99-4960-aeb7-aa1589d6bf30, 创建订单表单组件; Principle_Applied: 组件化设计;}}
-->

<template>
  <a-modal
    :title="isEdit ? '编辑订单' : '新增订单'"
    :open="visible"
    :width="800"
    :confirm-loading="submitting"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <div class="order-form">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        @finish="handleSubmit"
      >
        <!-- 基本信息 -->
        <a-divider orientation="left">基本信息</a-divider>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="订单编号" name="code">
              <a-input
                v-model:value="formData.code"
                placeholder="请输入订单编号"
                :maxlength="100"
              >
                <template #suffix>
                  <a-button type="link" size="small" @click="generateCode">
                    生成
                  </a-button>
                </template>
              </a-input>
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="订单类型" name="type">
              <a-select
                v-model:value="formData.type"
                placeholder="请选择订单类型"
                :options="typeOptions"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="客户名称" name="customerName">
              <a-input
                v-model:value="formData.customerName"
                placeholder="请输入客户名称"
                :maxlength="200"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="业务员" name="salesman">
              <a-input
                v-model:value="formData.salesman"
                placeholder="请输入业务员"
                :maxlength="100"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="下单日期" name="orderDate">
              <a-date-picker
                v-model:value="formData.orderDate"
                placeholder="请选择下单日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="交货日期" name="deliveryDate">
              <a-date-picker
                v-model:value="formData.deliveryDate"
                placeholder="请选择交货日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="订单量" name="orderQuantity">
              <a-input-number
                v-model:value="formData.orderQuantity"
                placeholder="请输入订单量"
                :min="0"
                :precision="0"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          
          <a-col :span="12">
            <a-form-item label="记量单位" name="orderUnitId">
              <a-select
                v-model:value="formData.orderUnitId"
                placeholder="请选择记量单位"
                :options="unitOptions"
                :loading="optionsLoading"
              />
            </a-form-item>
          </a-col>
        </a-row>
        
        <a-form-item label="订单备注" name="remark">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入订单备注"
            :rows="3"
            :maxlength="500"
            show-count
          />
        </a-form-item>

        <!-- 花样信息 -->
        <a-divider orientation="left">
          花样信息
          <a-button type="link" size="small" @click="addPattern">
            <PlusOutlined />
            添加花样
          </a-button>
        </a-divider>
        
        <div class="pattern-list">
          <div
            v-for="(pattern, index) in formData.patternInfo"
            :key="index"
            class="pattern-item"
          >
            <a-card size="small" :title="`花样 ${index + 1}`">
              <template #extra>
                <a-button
                  type="link"
                  size="small"
                  danger
                  @click="removePattern(index)"
                  :disabled="formData.patternInfo.length <= 1"
                >
                  <DeleteOutlined />
                  删除
                </a-button>
              </template>
              
              <a-row :gutter="16">
                <a-col :span="16">
                  <a-form-item
                    :name="['patternInfo', index, 'patternId']"
                    label="花样"
                    :rules="[{ required: true, message: '请选择花样' }]"
                  >
                    <a-select
                      v-model:value="pattern.patternId"
                      placeholder="请选择花样"
                      :options="patternOptions"
                      :loading="optionsLoading"
                      show-search
                      :filter-option="filterOption"
                    />
                  </a-form-item>
                </a-col>
                
                <a-col :span="8">
                  <a-form-item
                    :name="['patternInfo', index, 'patternQuantity']"
                    label="数量"
                    :rules="[
                      { required: true, message: '请输入数量' },
                      { type: 'number', min: 1, message: '数量必须大于0' }
                    ]"
                  >
                    <a-input-number
                      v-model:value="pattern.patternQuantity"
                      placeholder="数量"
                      :min="1"
                      :precision="0"
                      style="width: 100%"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-card>
          </div>
        </div>

        <!-- 文件上传 -->
        <a-divider orientation="left">订单文件</a-divider>
        
        <a-form-item label="订单文件" name="orderFiles">
          <a-upload
            v-model:file-list="fileList"
            :multiple="true"
            :before-upload="beforeUpload"
            :remove="handleRemoveFile"
            :custom-request="handleUpload"
            list-type="text"
            :max-count="10"
          >
            <a-button>
              <UploadOutlined />
              选择文件
            </a-button>
            <div class="upload-tip">
              支持上传图片、PDF、Word、Excel等格式，单个文件不超过10MB，最多10个文件
            </div>
          </a-upload>
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance, UploadFile } from 'ant-design-vue';
import {
  PlusOutlined,
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons-vue';
import { orderApi, orderFileApi, OrderUtils } from '@/api/order';
import { tagApi } from '@/api/tag';
import type {
  Order,
  CreateOrderRequest,
  UpdateOrderRequest,
  PatternInfo,
  OrderType
} from '@/types/order';
import { ORDER_TYPE_OPTIONS } from '@/types/order';
import { TagType, TagStatus } from '@/types/tag';

// Props
interface Props {
  visible: boolean;
  isEdit: boolean;
  order?: Order;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  isEdit: false,
  order: undefined
});

// Emits
const emit = defineEmits<{
  success: [];
  cancel: [];
}>();

// 响应式数据
const formRef = ref<FormInstance>();
const submitting = ref(false);
const optionsLoading = ref(false);
const fileList = ref<UploadFile[]>([]);

// 表单数据
const formData = reactive<CreateOrderRequest & UpdateOrderRequest>({
  type: 1,
  code: '',
  customerName: '',
  salesman: '',
  orderDate: '',
  deliveryDate: '',
  orderUnitId: undefined,
  orderQuantity: 0,
  remark: '',
  orderFiles: [],
  patternInfo: [
    {
      patternId: '',
      patternQuantity: 1
    }
  ]
});

// 选项数据
const unitOptions = ref<Array<{ label: string; value: number }>>([]);
const patternOptions = ref<Array<{ label: string; value: string }>>([]);

// 订单类型选项
const typeOptions = computed(() =>
  ORDER_TYPE_OPTIONS.map(item => ({
    label: item.label,
    value: item.value
  }))
);

// 表单验证规则
const formRules = {
  code: [
    { required: true, message: '请输入订单编号' },
    { min: 3, max: 100, message: '订单编号长度为3-100个字符' },
    {
      validator: (_: any, value: string) => {
        if (value && !OrderUtils.validateOrderCode(value)) {
          return Promise.reject('订单编号格式不正确，只能包含字母、数字、横线和下划线');
        }
        return Promise.resolve();
      }
    }
  ],
  type: [
    { required: true, message: '请选择订单类型' }
  ],
  customerName: [
    { required: true, message: '请输入客户名称' },
    { max: 200, message: '客户名称不能超过200个字符' },
    {
      validator: (_: any, value: string) => {
        if (value && !OrderUtils.validateCustomerName(value)) {
          return Promise.reject('客户名称格式不正确');
        }
        return Promise.resolve();
      }
    }
  ],
  salesman: [
    { max: 100, message: '业务员名称不能超过100个字符' },
    {
      validator: (_: any, value: string) => {
        if (value && !OrderUtils.validateSalesman(value)) {
          return Promise.reject('业务员名称格式不正确');
        }
        return Promise.resolve();
      }
    }
  ],
  orderDate: [
    { required: true, message: '请选择下单日期' }
  ],
  deliveryDate: [
    { required: true, message: '请选择交货日期' },
    {
      validator: (_: any, value: string) => {
        if (value && formData.orderDate && new Date(value) < new Date(formData.orderDate)) {
          return Promise.reject('交货日期不能早于下单日期');
        }
        return Promise.resolve();
      }
    }
  ],
  orderQuantity: [
    { required: true, message: '请输入订单量' },
    { type: 'number', min: 0, message: '订单量不能为负数' },
    {
      validator: (_: any, value: number) => {
        if (value !== undefined && !OrderUtils.validateOrderQuantity(value)) {
          return Promise.reject('订单量必须是非负整数');
        }
        return Promise.resolve();
      }
    }
  ],
  orderUnitId: [
    { required: true, message: '请选择记量单位' }
  ],
  remark: [
    { max: 500, message: '备注不能超过500个字符' }
  ]
};

// 工具函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 生成订单编号
const generateCode = () => {
  const timestamp = Date.now().toString().slice(-8);
  const random = Math.random().toString(36).substring(2, 6).toUpperCase();
  formData.code = `ORD${timestamp}${random}`;
};

// 添加花样
const addPattern = () => {
  formData.patternInfo.push({
    patternId: '',
    patternQuantity: 1
  });
};

// 删除花样
const removePattern = (index: number) => {
  if (formData.patternInfo.length > 1) {
    formData.patternInfo.splice(index, 1);
  }
};

// 文件上传前检查
const beforeUpload = (file: File) => {
  // 检查文件类型
  if (!OrderUtils.isAllowedFileType(file.name)) {
    message.error('不支持的文件格式');
    return false;
  }

  // 检查文件大小（10MB）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB');
    return false;
  }

  return false; // 阻止自动上传，使用自定义上传
};

// 自定义文件上传
const handleUpload = async (options: any) => {
  try {
    const { file } = options;
    const response = await orderFileApi.uploadOrderFile(file);

    // 更新文件列表状态
    const fileItem = fileList.value.find(item => item.uid === file.uid);
    if (fileItem) {
      fileItem.status = 'done';
      fileItem.url = response.url;
      fileItem.response = response;
    }

    // 更新表单数据
    if (!formData.orderFiles) {
      formData.orderFiles = [];
    }
    formData.orderFiles.push(response.url);

    message.success('文件上传成功');
  } catch (error) {
    console.error('文件上传失败:', error);

    // 更新文件列表状态
    const fileItem = fileList.value.find(item => item.uid === options.file.uid);
    if (fileItem) {
      fileItem.status = 'error';
    }

    message.error('文件上传失败');
  }
};

// 删除文件
const handleRemoveFile = async (file: UploadFile) => {
  try {
    if (file.url) {
      await orderFileApi.deleteOrderFile(file.url);

      // 从表单数据中移除
      if (formData.orderFiles) {
        const index = formData.orderFiles.indexOf(file.url);
        if (index > -1) {
          formData.orderFiles.splice(index, 1);
        }
      }
    }

    return true;
  } catch (error) {
    console.error('删除文件失败:', error);
    message.error('删除文件失败');
    return false;
  }
};

// 获取选项数据
const getOptions = async () => {
  try {
    optionsLoading.value = true;
    const options = await orderApi.getSearchOptions();

    // 设置花样选项
    patternOptions.value = options.patterns.map(item => ({
      label: `${item.name} (${item.code || item.id})`,
      value: item.id.toString()
    }));

    // 获取计量单位选项
    try {
      const unitResponse = await tagApi.getTagList({
        type: TagType.PATTERN_UNIT,
        status: TagStatus.ENABLED,
        page: 1,
        pageSize: 100 // 获取所有启用的计量单位
      });

      unitOptions.value = unitResponse.tags.map(item => ({
        label: item.name,
        value: item.id
      }));
    } catch (error) {
      console.error('获取计量单位选项失败:', error);
      // 如果API调用失败，使用默认选项
      unitOptions.value = [
        { label: '件', value: 1 },
        { label: '套', value: 2 },
        { label: '个', value: 3 },
        { label: '米', value: 4 },
        { label: '码', value: 5 }
      ];
    }
  } catch (error) {
    console.error('获取选项数据失败:', error);
  } finally {
    optionsLoading.value = false;
  }
};

// 初始化表单数据
const initFormData = () => {
  if (props.isEdit && props.order) {
    // 编辑模式，填充现有数据
    Object.assign(formData, {
      type: props.order.type,
      code: props.order.code || '',
      customerName: props.order.customerName || '',
      salesman: props.order.salesman || '',
      orderDate: props.order.orderDate || '',
      deliveryDate: props.order.deliveryDate || '',
      orderUnitId: props.order.orderUnitId,
      orderQuantity: props.order.orderQuantity || 0,
      remark: props.order.remark || '',
      orderFiles: props.order.orderFiles || [],
      patternInfo: props.order.patternInfo && props.order.patternInfo.length > 0
        ? props.order.patternInfo
        : [{ patternId: '', patternQuantity: 1 }]
    });

    // 设置文件列表
    if (props.order.orderFiles && props.order.orderFiles.length > 0) {
      fileList.value = props.order.orderFiles.map((url, index) => ({
        uid: `file-${index}`,
        name: url.split('/').pop() || `文件${index + 1}`,
        status: 'done',
        url: url
      }));
    }
  } else {
    // 新增模式，重置表单
    Object.assign(formData, {
      type: 1,
      code: '',
      customerName: '',
      salesman: '',
      orderDate: '',
      deliveryDate: '',
      orderUnitId: undefined,
      orderQuantity: 0,
      remark: '',
      orderFiles: [],
      patternInfo: [{ patternId: '', patternQuantity: 1 }]
    });

    fileList.value = [];
  }
};

// 表单提交
const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitting.value = true;

    // 准备提交数据
    const submitData = {
      ...formData,
      patternInfo: formData.patternInfo.filter(item => item.patternId && item.patternQuantity > 0)
    };

    if (props.isEdit && props.order) {
      // 编辑订单
      const updateData: UpdateOrderRequest = submitData;
      await orderApi.updateOrder(props.order.id, updateData);
      message.success('更新订单成功');
    } else {
      // 创建订单
      const createData: CreateOrderRequest = submitData;
      await orderApi.createOrder(createData);
      message.success('创建订单成功');
    }

    emit('success');
  } catch (error) {
    console.error('提交表单失败:', error);
    if (error instanceof Error) {
      message.error(error.message || '操作失败，请稍后重试');
    } else {
      message.error('操作失败，请稍后重试');
    }
  } finally {
    submitting.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
};

// 监听props变化
watch(() => [props.order, props.isEdit, props.visible], () => {
  if (props.visible) {
    initFormData();
    // 只有在模态框显示时才获取选项数据
    getOptions();
  }
}, { immediate: true });

// 生命周期 - 移除onMounted中的getOptions调用
</script>

<style scoped>
.order-form {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;
}

.pattern-list {
  margin-bottom: 16px;
}

.pattern-item {
  margin-bottom: 16px;
}

.pattern-item:last-child {
  margin-bottom: 0;
}

.upload-tip {
  margin-top: 8px;
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}

/* 表单样式优化 */
:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 24px 0 16px 0;
}

:deep(.ant-divider-horizontal.ant-divider-with-text-left::before) {
  width: 5%;
}

:deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.ant-card-extra) {
  font-size: 12px;
}

/* 上传组件样式 */
:deep(.ant-upload-list) {
  margin-top: 8px;
}

:deep(.ant-upload-list-item) {
  padding: 8px 12px;
  border-radius: 6px;
}

:deep(.ant-upload-list-item-name) {
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-form {
    max-height: 60vh;
  }

  :deep(.ant-col) {
    margin-bottom: 8px;
  }

  :deep(.ant-modal) {
    margin: 0;
    max-width: 100vw;
    top: 0;
    padding-bottom: 0;
  }

  :deep(.ant-modal-content) {
    border-radius: 0;
  }

  .pattern-item {
    margin-bottom: 12px;
  }

  :deep(.ant-card) {
    font-size: 13px;
  }

  :deep(.ant-form-item-label) {
    font-size: 13px;
  }
}

/* 滚动条样式 */
.order-form::-webkit-scrollbar {
  width: 6px;
}

.order-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.order-form::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.order-form::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表单项间距优化 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}

/* 按钮样式 */
:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
  font-size: 12px;
}

/* 输入框样式 */
:deep(.ant-input),
:deep(.ant-select-selector),
:deep(.ant-picker) {
  border-radius: 6px;
}

:deep(.ant-input-number) {
  width: 100%;
  border-radius: 6px;
}

/* 卡片样式 */
:deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  min-height: 40px;
  padding: 0 12px;
}

:deep(.ant-card-body) {
  padding: 12px;
}
</style>
