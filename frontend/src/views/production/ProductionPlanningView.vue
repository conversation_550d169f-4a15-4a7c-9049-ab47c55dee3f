<!--
  生产计划页面 - 拖拽式排产管理
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-28 20:00:00 +08:00; Reason: Shrimp Task ID: #e57cdada-fe6f-4ea8-abc2-e23e651da05d, 创建生产计划主页面组件; Principle_Applied: 组件化设计、布局复用;}}
-->

<template>
  <div class="production-planning">
    <!-- Header: 页面标题和操作 -->
    <div class="header">
      <a-card :bordered="false" class="header-card">
        <div class="header-content">
          <div class="title-section">
            <h2 class="page-title">生产计划管理</h2>
            <p class="page-description">拖拽式订单分配和生产排序</p>
          </div>
          <div class="action-section">
            <a-space>
              <a-button @click="handleRefresh" :loading="refreshing">
                <template #icon>
                  <ReloadOutlined />
                </template>
                刷新数据
              </a-button>
              <a-button type="primary" @click="handleProductionHistory">
                <template #icon>
                  <HistoryOutlined />
                </template>
                生产历史
              </a-button>
            </a-space>
          </div>
        </div>
      </a-card>
    </div>

    <!-- Content: 三列布局 -->
    <div class="content">
      <div class="production-content">
        <!-- Left: 设备列表 -->
        <div class="device-panel">
          <a-card title="设备列表" size="small" class="panel-card">
            <template #extra>
              <a-badge :count="deviceList.length" :number-style="{ backgroundColor: '#52c41a' }" />
            </template>
            <!-- 设备列表组件 -->
            <DeviceList
              ref="deviceListRef"
              :auto-select-first="true"
              @device-selected="handleDeviceSelected"
              @devices-loaded="handleDevicesLoaded"
            />
          </a-card>
        </div>

        <!-- Middle: 已分配订单 -->
        <div class="assigned-panel">
          <a-card
            title="已分配订单队列"
            size="small"
            class="panel-card"
          >
            <template #extra>
              <a-space>
                <span v-if="selectedDevice" class="selected-device">
                  {{ selectedDevice.name }}
                </span>
                <a-badge :count="assignedOrders.length" :number-style="{ backgroundColor: '#1890ff' }" />
              </a-space>
            </template>
            <!-- 已分配订单组件 -->
            <AssignedOrderList
              ref="assignedOrderListRef"
              :selected-device="selectedDevice"
              @sequence-created="handleSequenceCreated"
              @sequence-removed="handleSequenceRemoved"
              @sequences-reordered="handleSequencesReordered"
              @sequence-drag-start="handleSequenceDragStart"
              @sequence-drag-end="handleSequenceDragEnd"
            />
          </a-card>
        </div>

        <!-- Right: 待分配订单 -->
        <div class="unassigned-panel">
          <a-card title="待分配订单" size="small" class="panel-card">
            <template #extra>
              <a-badge :count="unassignedOrders.length" :number-style="{ backgroundColor: '#faad14' }" />
            </template>
            <!-- 待分配订单组件 -->
            <UnassignedOrderList
              ref="unassignedOrderListRef"
              @orders-loaded="handleUnassignedOrdersLoaded"
              @drag-start="handleOrderDragStart"
              @drag-end="handleOrderDragEnd"
              @sequence-removed="handleSequenceRemovedFromUnassigned"
              @sequences-created="handleSequencesCreated"
            />
          </a-card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ReloadOutlined, HistoryOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '../../stores/user'
import { hasAllPermissions } from '../../utils/permission'
import DeviceList from '../../components/production/DeviceList.vue'
import UnassignedOrderList from '../../components/production/UnassignedOrderList.vue'
import AssignedOrderList from '../../components/production/AssignedOrderList.vue'
import type { Device } from '../../types/device'
import type { Order, DeviceOrderSequence } from '../../types/order'

// 权限检查
const userStore = useUserStore()
const hasProductionPermission = computed(() => userStore.hasPermissions(['production:view']))

// 响应式数据
const refreshing = ref(false)
const deviceList = ref<Device[]>([])
const assignedOrders = ref([])
const unassignedOrders = ref<Order[]>([])
const selectedDevice = ref<Device | null>(null)
const isDraggingGlobal = ref(false)
const dragType = ref<'order' | 'sequence' | null>(null)

// 组件引用
const deviceListRef = ref()
const unassignedOrderListRef = ref()
const assignedOrderListRef = ref()

// 页面操作
const handleRefresh = async () => {
  try {
    refreshing.value = true

    // 刷新设备列表
    if (deviceListRef.value) {
      deviceListRef.value.refreshDevices()
    }

    // 刷新待分配订单列表
    if (unassignedOrderListRef.value) {
      unassignedOrderListRef.value.refreshOrders()
    }

    // 刷新已分配订单列表
    if (assignedOrderListRef.value) {
      assignedOrderListRef.value.refreshSequences()
    }
    message.success('数据刷新成功')
  } catch (error) {
    console.error('刷新数据失败:', error)
    message.error('刷新数据失败')
  } finally {
    refreshing.value = false
  }
}

const handleProductionHistory = () => {
  // TODO: 跳转到生产历史页面
  message.info('生产历史功能将在后续版本中实现')
}

// 设备相关处理
const handleDeviceSelected = (device: Device | null) => {
  selectedDevice.value = device
  if (device) {
    console.log('选中设备:', device.name)
    // TODO: 加载该设备的已分配订单
  } else {
    console.log('取消选择设备')
  }
}

const handleDevicesLoaded = (devices: Device[]) => {
  deviceList.value = devices
  console.log('设备列表加载完成:', devices.length, '台设备')
}

// 待分配订单相关处理
const handleUnassignedOrdersLoaded = (orders: Order[]) => {
  unassignedOrders.value = orders
  console.log('待分配订单加载完成:', orders.length, '个订单')
}

const handleOrderDragStart = (order: Order) => {
  console.log('开始拖拽订单:', order.code || order.id)
  isDraggingGlobal.value = true
  dragType.value = 'order'

  // 添加全局拖拽样式
  document.body.classList.add('dragging-order')
}

const handleOrderDragEnd = () => {
  console.log('订单拖拽结束')
  isDraggingGlobal.value = false
  dragType.value = null

  // 移除全局拖拽样式
  document.body.classList.remove('dragging-order', 'dragging-sequence')
}

// 已分配订单相关处理
const handleSequenceCreated = (sequence: DeviceOrderSequence) => {
  console.log('排产记录创建成功:', sequence.id)
  // 刷新待分配订单列表
  if (unassignedOrderListRef.value) {
    unassignedOrderListRef.value.refreshOrders()
  }
  // 刷新已分配订单列表
  if (assignedOrderListRef.value) {
    assignedOrderListRef.value.refreshSequences()
  }
}

const handleSequenceRemoved = (sequenceId: number) => {
  console.log('排产记录移除成功:', sequenceId)
  // 刷新待分配订单列表
  if (unassignedOrderListRef.value) {
    unassignedOrderListRef.value.refreshOrders()
  }
  // 刷新已分配订单列表
  if (assignedOrderListRef.value) {
    assignedOrderListRef.value.refreshSequences()
  }
}

const handleSequencesReordered = (deviceId: number) => {
  console.log('设备排产顺序调整成功:', deviceId)
}

const handleSequenceRemovedFromUnassigned = (sequenceId: number) => {
  console.log('从待分配区域移除排产记录:', sequenceId)
  // 刷新已分配订单列表
  if (assignedOrderListRef.value) {
    assignedOrderListRef.value.refreshSequences()
  }
}

const handleSequencesCreated = () => {
  console.log('订单拆分成功，刷新已分配订单队列')
  // 刷新已分配订单列表
  if (assignedOrderListRef.value) {
    assignedOrderListRef.value.refreshSequences()
  }
}

const handleSequenceDragStart = (sequence: DeviceOrderSequence) => {
  console.log('开始拖拽排产记录:', sequence.id)
  isDraggingGlobal.value = true
  dragType.value = 'sequence'

  // 添加全局拖拽样式
  document.body.classList.add('dragging-sequence')
}

const handleSequenceDragEnd = () => {
  console.log('排产记录拖拽结束')
  isDraggingGlobal.value = false
  dragType.value = null

  // 移除全局拖拽样式
  document.body.classList.remove('dragging-order', 'dragging-sequence')
}

// 测试卡片点击
const handleCardClick = () => {
  console.log('已分配订单卡片被点击了！')
  message.info('卡片响应正常')
}

// 卡片级别的拖拽事件处理
const handleCardDragOver = (event: DragEvent) => {
  console.log('卡片级别：拖拽经过')
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

const handleCardDragEnter = (event: DragEvent) => {
  console.log('卡片级别：拖拽进入')
  event.preventDefault()
}

const handleCardDragLeave = (event: DragEvent) => {
  console.log('卡片级别：拖拽离开')
  event.preventDefault()
}

const handleCardDrop = async (event: DragEvent) => {
  console.log('卡片级别：拖拽放置')
  event.preventDefault()

  if (!selectedDevice.value) {
    message.error('请先选择设备')
    return
  }

  try {
    const dragDataString = event.dataTransfer!.getData('application/json')
    console.log('卡片级别拖拽数据:', dragDataString)

    if (dragDataString) {
      const dragData = JSON.parse(dragDataString)
      console.log('卡片级别解析的拖拽数据:', dragData)

      // 直接调用AssignedOrderList的方法
      if (assignedOrderListRef.value && dragData.id && dragData.status !== undefined) {
        // 这里我们需要手动触发AssignedOrderList的创建排产记录逻辑
        message.success('在卡片级别检测到订单拖拽，正在处理...')
      }
    }
  } catch (error) {
    console.error('卡片级别拖拽处理失败:', error)
  }
}

// 页面初始化
onMounted(() => {
  if (!hasProductionPermission.value) {
    message.error('您没有访问生产计划的权限')
    return
  }

  // TODO: 初始化数据加载
  console.log('生产计划页面初始化')
})
</script>

<style scoped>
.production-planning {
  padding: 16px;
  background: #f5f5f5;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Header: 页面标题和操作 */
.header {
  flex-shrink: 0;
}

.header-card {
  border-radius: 8px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  flex: 1;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

.page-description {
  margin: 4px 0 0 0;
  color: #8c8c8c;
  font-size: 14px;
}

.action-section {
  flex-shrink: 0;
}

/* Content: 三列布局容器 */
.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.production-content {
  flex: 1;
  display: flex;
  gap: 16px;
  min-height: 0;
}

/* Left: 设备列表面板 */
.device-panel {
  width: 280px;
  flex-shrink: 0;
}

/* Middle: 已分配订单面板 */
.assigned-panel {
  flex: 1;
}

/* Right: 待分配订单面板 */
.unassigned-panel {
  flex: 1;
}

/* 面板卡片样式 */
.panel-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.panel-card :deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  padding: 12px 16px;
  min-height: auto;
}

.panel-card :deep(.ant-card-head-title) {
  font-size: 14px;
  font-weight: 600;
}

.panel-card :deep(.ant-card-body) {
  padding: 16px;
  height: calc(100% - 57px);
  overflow: hidden;
  position: relative;
}

/* 确保拖拽区域可以接收事件 */
.assigned-panel .panel-card :deep(.ant-card-body) {
  overflow: visible;
}

/* 选中设备显示 */
.selected-device {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

/* 组件占位样式 */
.component-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
}

/* 全局拖拽状态样式 */
:global(.dragging-order) .assigned-panel .panel-card {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

:global(.dragging-sequence) .unassigned-panel .panel-card {
  border-color: #52c41a;
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

/* 完全禁用覆盖层 - 它会干扰拖拽事件 */
/*
:global(.dragging-order) .production-content::before,
:global(.dragging-sequence) .production-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.02);
  pointer-events: none;
  z-index: 0;
}
*/

/* 响应式设计 */
@media (max-width: 1200px) {
  .device-panel {
    width: 240px;
  }

  .assigned-panel,
  .unassigned-panel {
    max-width: 350px; /* 在较小屏幕上稍微减小最大宽度 */
  }
}

@media (max-width: 992px) {
  .production-content {
    flex-direction: column;
  }

  .device-panel,
  .assigned-panel,
  .unassigned-panel {
    width: 100% !important;
    flex: none !important;
    height: 300px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
}

@media (max-width: 768px) {
  .production-planning {
    padding: 8px;
  }

  .device-panel,
  .assigned-panel,
  .unassigned-panel {
    height: 250px;
  }

  .page-title {
    font-size: 18px;
  }
}
</style>
