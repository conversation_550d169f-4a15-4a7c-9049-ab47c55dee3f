<!--
  订单管理页面
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-28 19:15:00 +08:00; Reason: Shrimp Task ID: #3170ed14-6a40-44ad-ab95-f54c0c7be358, 重构订单管理页面; Principle_Applied: 功能完整性、用户体验优化;}}
-->

<template>
  <div class="order-management">
    <!-- 搜索区域 -->
    <a-card :bordered="false" class="search-card">
      <a-form layout="inline" :model="searchForm" class="search-form">
        <a-form-item label="订单编号">
          <a-input
            v-model:value="searchForm.search"
            placeholder="请输入订单编号或客户名称"
            allow-clear
            style="width: 200px"
            @press-enter="handleSearch"
          />
        </a-form-item>

        <a-form-item label="订单状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择订单状态"
            allow-clear
            style="width: 120px"
            :options="statusOptions"
          />
        </a-form-item>

        <a-form-item label="订单类型">
          <a-select
            v-model:value="searchForm.type"
            placeholder="请选择订单类型"
            allow-clear
            style="width: 120px"
            :options="typeOptions"
          />
        </a-form-item>

        <a-form-item label="客户名称">
          <a-select
            v-model:value="searchForm.customerName"
            placeholder="请选择客户"
            allow-clear
            style="width: 150px"
            :options="customerOptions"
            :loading="optionsLoading"
            show-search
            :filter-option="filterOption"
          />
        </a-form-item>

        <a-form-item label="业务员">
          <a-select
            v-model:value="searchForm.salesman"
            placeholder="请选择业务员"
            allow-clear
            style="width: 120px"
            :options="salesmanOptions"
            :loading="optionsLoading"
            show-search
            :filter-option="filterOption"
          />
        </a-form-item>

        <a-form-item label="下单日期">
          <a-range-picker
            v-model:value="orderDateRange"
            style="width: 240px"
            @change="handleOrderDateChange"
          />
        </a-form-item>

        <a-form-item label="交货日期">
          <a-range-picker
            v-model:value="deliveryDateRange"
            style="width: 240px"
            @change="handleDeliveryDateChange"
          />
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 订单列表区域 -->
    <a-card :bordered="false" class="table-card">
      <template #title>
        <span>订单列表</span>
        <a-tag color="blue" style="margin-left: 8px">
          共 {{ pagination.total }} 个订单
        </a-tag>
      </template>

      <template #extra>
        <a-space>
          <a-button type="primary" @click="showCreateModal">
            <PlusOutlined />
            新增订单
          </a-button>
          <a-button @click="handleExport" :loading="exportLoading">
            <ExportOutlined />
            导出
          </a-button>
          <a-button @click="showUnitManagement">
            <TagOutlined />
            计量单位
          </a-button>
        </a-space>
      </template>

      <a-table
        :columns="columns"
        :data-source="orderList"
        :loading="loading"
        :pagination="tablePagination"
        :scroll="{ x: 1400 }"
        row-key="id"
        :row-selection="rowSelection"
        @change="handleTableChange"
      >
        <!-- 订单编号列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'code'">
            <div class="order-code">
              <strong>{{ record.code || '-' }}</strong>
              <div class="order-id" v-if="record.id">
                ID: {{ record.id }}
              </div>
            </div>
          </template>

          <!-- 客户信息列 -->
          <template v-else-if="column.key === 'customer'">
            <div class="customer-info">
              <div class="customer-name">{{ record.customerName || '-' }}</div>
              <div class="salesman" v-if="record.salesman">
                业务员: {{ record.salesman }}
              </div>
            </div>
          </template>

          <!-- 订单类型列 -->
          <template v-else-if="column.key === 'type'">
            <a-tag :color="getOrderTypeColor(record.type)">
              {{ getOrderTypeLabel(record.type) }}
            </a-tag>
          </template>

          <!-- 订单状态列 -->
          <template v-else-if="column.key === 'status'">
            <a-tag :color="getOrderStatusColor(record.status)">
              {{ getOrderStatusLabel(record.status) }}
            </a-tag>
          </template>

          <!-- 订单数量列 -->
          <template v-else-if="column.key === 'quantity'">
            <div class="quantity-info">
              <div class="order-quantity">{{ record.orderQuantity || 0 }}</div>
              <div class="pattern-count" v-if="record.patternInfo && record.patternInfo.length">
                {{ record.patternInfo.length }} 个花样
              </div>
            </div>
          </template>

          <!-- 日期列 -->
          <template v-else-if="column.key === 'orderDate'">
            {{ formatDate(record.orderDate) }}
          </template>

          <template v-else-if="column.key === 'deliveryDate'">
            <div class="delivery-date">
              <div>{{ formatDate(record.deliveryDate) }}</div>
              <div v-if="isOrderDelayed(record.deliveryDate)" class="delay-tag">
                <a-tag color="red" size="small">
                  延期 {{ calculateDelayDays(record.deliveryDate) }} 天
                </a-tag>
              </div>
            </div>
          </template>

          <!-- 创建时间列 -->
          <template v-else-if="column.key === 'createdAt'">
            {{ formatDateTime(record.createdAt) }}
          </template>

          <!-- 操作列 -->
          <template v-else-if="column.key === 'actions'">
            <a-space>
              <a-button type="link" size="small" @click="showDetailModal(record)">
                <EyeOutlined />
                详情
              </a-button>
              <a-button
                type="link"
                size="small"
                @click="showEditModal(record)"
                :disabled="!canEditOrder(record.status)"
              >
                <EditOutlined />
                编辑
              </a-button>
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item key="status" @click="showStatusModal(record)">
                      <TagOutlined />
                      状态管理
                    </a-menu-item>
                    <a-menu-item key="sequence" @click="showSequenceModal(record)">
                      <ScheduleOutlined />
                      排产管理
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item
                      key="delete"
                      @click="showDeleteConfirm(record)"
                      :disabled="!canDeleteOrder(record.status)"
                      danger
                    >
                      <DeleteOutlined />
                      删除
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" size="small">
                  更多
                  <DownOutlined />
                </a-button>
              </a-dropdown>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 批量操作栏 -->
    <div v-if="selectedRowKeys.length > 0" class="batch-actions">
      <a-card :bordered="false">
        <a-space>
          <span>已选择 {{ selectedRowKeys.length }} 项</span>
          <a-button @click="handleBatchDelete" danger>
            <DeleteOutlined />
            批量删除
          </a-button>
          <a-button @click="handleBatchExport">
            <ExportOutlined />
            批量导出
          </a-button>
          <a-button @click="clearSelection">
            取消选择
          </a-button>
        </a-space>
      </a-card>
    </div>

    <!-- 订单表单模态框 -->
    <OrderFormModal
      :visible="formModalVisible"
      :is-edit="isEditMode"
      :order="currentOrder"
      @success="handleFormSuccess"
      @cancel="handleFormCancel"
    />

    <!-- 订单详情模态框 -->
    <OrderDetailModal
      :visible="detailModalVisible"
      :order-id="currentOrder?.id"
      @cancel="handleDetailCancel"
      @edit="handleDetailEdit"
      @sequence-manage="handleDetailSequenceManage"
    />

    <!-- 设备排产管理模态框 -->
    <DeviceSchedulingModal
      :visible="schedulingModalVisible"
      :order="currentOrder"
      @success="handleSchedulingSuccess"
      @cancel="handleSchedulingCancel"
    />

    <!-- 计量单位管理模态框 -->
    <TagManagementModal
      :visible="unitManagementVisible"
      :tag-type="TagType.PATTERN_UNIT"
      title="计量单位管理"
      @success="handleUnitManagementSuccess"
      @cancel="handleUnitManagementCancel"
      @update:visible="unitManagementVisible = $event"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import type { TableColumnsType, TableProps } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  ExportOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  DownOutlined,
  TagOutlined,
  ScheduleOutlined
} from '@ant-design/icons-vue';
import { orderApi } from '@/api/order';
import type {
  Order,
  OrderListQuery,
  OrderStatus,
  OrderType,
  OrderSearchOptions
} from '@/types/order';
import {
  ORDER_STATUS_OPTIONS,
  ORDER_TYPE_OPTIONS,
  getOrderStatusLabel,
  getOrderStatusColor,
  getOrderTypeLabel,
  getOrderTypeColor,
  canEditOrder,
  canDeleteOrder,
  calculateDelayDays,
  isOrderDelayed,
  formatOrderDate as formatDate,
  formatOrderDateTime as formatDateTime
} from '@/types/order';
import { OrderUtils } from '@/api/order';
import { TagType } from '@/types/tag';
import OrderFormModal from '@/components/order/OrderFormModal.vue';
import OrderDetailModal from '@/components/order/OrderDetailModal.vue';
import DeviceSchedulingModal from '@/components/order/DeviceSchedulingModal.vue';
import TagManagementModal from '@/components/tag/TagManagementModal.vue';

// 响应式数据
const loading = ref(false);
const exportLoading = ref(false);
const optionsLoading = ref(false);
const orderList = ref<Order[]>([]);
const selectedRowKeys = ref<number[]>([]);

// 搜索表单
const searchForm = reactive<OrderListQuery>({
  page: 1,
  pageSize: 10,
  search: '',
  status: undefined,
  type: undefined,
  customerName: '',
  salesman: '',
  orderDateStart: '',
  orderDateEnd: '',
  deliveryDateStart: '',
  deliveryDateEnd: ''
});

// 日期范围
const orderDateRange = ref<[string, string] | null>(null);
const deliveryDateRange = ref<[string, string] | null>(null);

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) =>
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
});

// 搜索选项
const searchOptions = ref<OrderSearchOptions>({
  customers: [],
  salesmen: [],
  patterns: [],
  devices: []
});

// 状态选项
const statusOptions = computed(() =>
  ORDER_STATUS_OPTIONS.map(item => ({
    label: item.label,
    value: item.value
  }))
);

// 类型选项
const typeOptions = computed(() =>
  ORDER_TYPE_OPTIONS.map(item => ({
    label: item.label,
    value: item.value
  }))
);

// 客户选项
const customerOptions = computed(() =>
  searchOptions.value.customers.map(item => ({
    label: `${item.name} (${item.count})`,
    value: item.name
  }))
);

// 业务员选项
const salesmanOptions = computed(() =>
  searchOptions.value.salesmen.map(item => ({
    label: `${item.name} (${item.count})`,
    value: item.name
  }))
);

// 表格列定义
const columns: TableColumnsType<Order> = [
  {
    title: '订单编号',
    key: 'code',
    dataIndex: 'code',
    width: 150,
    fixed: 'left'
  },
  {
    title: '客户信息',
    key: 'customer',
    width: 180
  },
  {
    title: '订单类型',
    key: 'type',
    dataIndex: 'type',
    width: 100,
    align: 'center'
  },
  {
    title: '订单状态',
    key: 'status',
    dataIndex: 'status',
    width: 100,
    align: 'center'
  },
  {
    title: '订单数量',
    key: 'quantity',
    width: 120,
    align: 'center'
  },
  {
    title: '下单日期',
    key: 'orderDate',
    dataIndex: 'orderDate',
    width: 120,
    align: 'center'
  },
  {
    title: '交货日期',
    key: 'deliveryDate',
    dataIndex: 'deliveryDate',
    width: 140,
    align: 'center'
  },
  {
    title: '创建时间',
    key: 'createdAt',
    dataIndex: 'createdAt',
    width: 160,
    align: 'center'
  },
  {
    title: '操作',
    key: 'actions',
    width: 180,
    fixed: 'right',
    align: 'center'
  }
];

// 表格分页配置
const tablePagination = computed(() => ({
  current: pagination.current,
  pageSize: pagination.pageSize,
  total: pagination.total,
  showSizeChanger: pagination.showSizeChanger,
  showQuickJumper: pagination.showQuickJumper,
  showTotal: pagination.showTotal
}));

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys;
  },
  getCheckboxProps: (record: Order) => ({
    disabled: !canDeleteOrder(record.status)
  })
}));

// 工具函数已从types/order.ts导入

// 过滤选项函数
const filterOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

// 获取订单列表
const getOrderList = async () => {
  try {
    loading.value = true;

    // 过滤掉空字符串参数
    const params: any = {
      page: pagination.current,
      pageSize: pagination.pageSize
    };

    // 只添加非空参数
    Object.keys(searchForm).forEach(key => {
      const value = searchForm[key as keyof typeof searchForm];
      if (value !== '' && value !== null && value !== undefined) {
        params[key] = value;
      }
    });

    const response = await orderApi.getOrderList(params);
    orderList.value = response.orders;
    pagination.total = response.total;
  } catch (error) {
    console.error('获取订单列表失败:', error);
    message.error('获取订单列表失败');
  } finally {
    loading.value = false;
  }
};

// 获取搜索选项
const getSearchOptions = async () => {
  // 防止重复调用
  if (optionsLoading.value || searchOptions.value.customers.length > 0) {
    return;
  }

  try {
    optionsLoading.value = true;
    const options = await orderApi.getSearchOptions();
    searchOptions.value = options;
  } catch (error) {
    console.error('获取搜索选项失败:', error);
  } finally {
    optionsLoading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  getOrderList();
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    page: 1,
    pageSize: 10,
    search: '',
    status: undefined,
    type: undefined,
    customerName: '',
    salesman: '',
    orderDateStart: '',
    orderDateEnd: '',
    deliveryDateStart: '',
    deliveryDateEnd: ''
  });
  orderDateRange.value = null;
  deliveryDateRange.value = null;
  pagination.current = 1;
  getOrderList();
};

// 刷新数据
const handleRefresh = () => {
  getOrderList();
};

// 表格变化处理
const handleTableChange: TableProps['onChange'] = (pag) => {
  if (pag) {
    pagination.current = pag.current || 1;
    pagination.pageSize = pag.pageSize || 10;
    getOrderList();
  }
};

// 日期范围变化处理
const handleOrderDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.orderDateStart = dates[0];
    searchForm.orderDateEnd = dates[1];
  } else {
    searchForm.orderDateStart = '';
    searchForm.orderDateEnd = '';
  }
};

const handleDeliveryDateChange = (dates: [string, string] | null) => {
  if (dates) {
    searchForm.deliveryDateStart = dates[0];
    searchForm.deliveryDateEnd = dates[1];
  } else {
    searchForm.deliveryDateStart = '';
    searchForm.deliveryDateEnd = '';
  }
};

// 模态框状态
const formModalVisible = ref(false);
const detailModalVisible = ref(false);
const schedulingModalVisible = ref(false);
const unitManagementVisible = ref(false);
const isEditMode = ref(false);
const currentOrder = ref<Order>();

// 显示新增模态框
const showCreateModal = () => {
  isEditMode.value = false;
  currentOrder.value = undefined;
  formModalVisible.value = true;
};

// 显示编辑模态框
const showEditModal = (record: Order) => {
  isEditMode.value = true;
  currentOrder.value = record;
  formModalVisible.value = true;
};

// 显示详情模态框
const showDetailModal = (record: Order) => {
  currentOrder.value = record;
  detailModalVisible.value = true;
};

// 显示状态管理模态框
const showStatusModal = (record: Order) => {
  // TODO: 实现状态管理模态框
  message.info('状态管理功能将在后续版本中实现');
};

// 显示排产管理模态框
const showSequenceModal = (record: Order) => {
  currentOrder.value = record;
  schedulingModalVisible.value = true;
};

// 显示计量单位管理模态框
const showUnitManagement = () => {
  unitManagementVisible.value = true;
};

// 显示删除确认
const showDeleteConfirm = (record: Order) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除订单"${record.code || record.id}"吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: () => handleDelete(record.id)
  });
};

// 删除订单
const handleDelete = async (id: number) => {
  try {
    await orderApi.deleteOrder(id);
    message.success('删除成功');
    getOrderList();
  } catch (error) {
    console.error('删除订单失败:', error);
    message.error('删除失败');
  }
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的订单');
    return;
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个订单吗？此操作不可恢复。`,
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        await orderApi.batchDeleteOrders(selectedRowKeys.value);
        message.success('批量删除成功');
        selectedRowKeys.value = [];
        getOrderList();
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    }
  });
};

// 导出订单
const handleExport = () => {
  // TODO: 实现导出功能
  message.info('导出功能将在后续版本中实现');
};

// 批量导出
const handleBatchExport = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要导出的订单');
    return;
  }
  // TODO: 实现批量导出功能
  message.info('批量导出功能将在后续版本中实现');
};

// 清除选择
const clearSelection = () => {
  selectedRowKeys.value = [];
};

// 表单模态框成功处理
const handleFormSuccess = () => {
  formModalVisible.value = false;
  getOrderList();
};

// 表单模态框取消处理
const handleFormCancel = () => {
  formModalVisible.value = false;
};

// 详情模态框取消处理
const handleDetailCancel = () => {
  detailModalVisible.value = false;
};

// 详情模态框编辑处理
const handleDetailEdit = (order: Order) => {
  detailModalVisible.value = false;
  showEditModal(order);
};

// 详情模态框排产管理处理
const handleDetailSequenceManage = (order: Order) => {
  detailModalVisible.value = false;
  showSequenceModal(order);
};

// 排产模态框成功处理
const handleSchedulingSuccess = () => {
  schedulingModalVisible.value = false;
  getOrderList();
};

// 排产模态框取消处理
const handleSchedulingCancel = () => {
  schedulingModalVisible.value = false;
};

// 计量单位管理成功处理
const handleUnitManagementSuccess = () => {
  unitManagementVisible.value = false;
};

// 计量单位管理取消处理
const handleUnitManagementCancel = () => {
  unitManagementVisible.value = false;
};

// 生命周期
onMounted(() => {
  getSearchOptions();
  getOrderList();
});
</script>

<style scoped>
.order-management {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.search-card {
  margin-bottom: 16px;
}

.search-form :deep(.ant-form-item) {
  margin-bottom: 16px;
}

.table-card :deep(.ant-card-head-title) {
  display: flex;
  align-items: center;
}

.order-code .order-id {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.customer-info .customer-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.customer-info .salesman {
  font-size: 12px;
  color: #666;
}

.quantity-info {
  text-align: center;
}

.quantity-info .order-quantity {
  font-weight: 500;
  font-size: 14px;
}

.quantity-info .pattern-count {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.delivery-date .delay-tag {
  margin-top: 4px;
}

.batch-actions {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
}

.batch-actions .ant-card {
  margin: 0;
  border-radius: 6px;
}

.text-gray {
  color: #999;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-form :deep(.ant-form-item) {
    margin-bottom: 12px;
  }

  .order-management {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .search-form :deep(.ant-form-item) {
    width: 100%;
    margin-bottom: 8px;
  }

  .search-form :deep(.ant-input),
  .search-form :deep(.ant-select),
  .search-form :deep(.ant-picker) {
    width: 100% !important;
  }

  .table-card :deep(.ant-table-wrapper .ant-table-thead > tr > th),
  .table-card :deep(.ant-table-wrapper .ant-table-tbody > tr > td) {
    padding: 8px 4px;
    font-size: 12px;
  }

  .batch-actions {
    left: 12px;
    right: 12px;
    transform: none;
  }

  .batch-actions :deep(.ant-space) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* 表格样式优化 */
:deep(.ant-table-wrapper .ant-table-thead > tr > th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.ant-table-wrapper .ant-table-tbody > tr:hover > td) {
  background-color: #f5f5f5;
}

:deep(.ant-table-wrapper .ant-table-row-selected > td) {
  background-color: #e6f7ff;
}

/* 卡片样式 */
:deep(.ant-card) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
}

/* 按钮样式 */
:deep(.ant-btn) {
  border-radius: 6px;
}

:deep(.ant-btn-link) {
  padding: 0;
  height: auto;
}

/* 标签样式 */
:deep(.ant-tag) {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 6px;
}

/* 分页样式 */
:deep(.ant-pagination) {
  margin-top: 16px;
  text-align: right;
}
</style>
