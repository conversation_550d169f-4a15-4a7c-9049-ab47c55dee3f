<!--
  工价设置页面
  {{CHENGQI: Action: Added; Timestamp: 2025-07-10 18:00:00 +08:00; Reason: 工资管理权限细化, 创建工价设置子页面; Principle_Applied: 模块化页面设计;}}
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-27 16:45:00 +08:00; Reason: Task-007 扩展前端工价设置页面, 支持三种工价类型和激励工价配置; Principle_Applied: 复杂表单设计;}}
-->

<template>
  <div class="salary-rates">
    <!-- 搜索区域 -->
    <a-card :bordered="false" class="search-card">
      <a-form layout="inline" :model="searchForm" class="search-form">
        <a-form-item label="搜索关键词">
          <a-input
            v-model:value="searchForm.search"
            placeholder="请输入花样名称、创建人"
            allow-clear
            style="width: 250px"
            @press-enter="handleSearch"
          />
        </a-form-item>

        <a-form-item label="工价类型">
          <a-select
            v-model:value="searchForm.type"
            placeholder="请选择工价类型"
            allow-clear
            style="width: 150px"
            @change="handleTypeChange"
          >
            <a-select-option
              v-for="typeOption in wageTypeOptions"
              :key="typeOption.value"
              :value="typeOption.value"
            >
              {{ typeOption.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="花样">
          <a-select
            v-model:value="searchForm.patternId"
            placeholder="请选择花样"
            allow-clear
            show-search
            style="width: 200px"
            :filter-option="filterPatternOption"
            @change="handlePatternChange"
          >
            <a-select-option
              v-for="pattern in searchOptions.patterns"
              :key="pattern.id"
              :value="pattern.id"
            >
              {{ pattern.name }}{{ pattern.code ? ` (${pattern.code})` : '' }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="创建人">
          <a-select
            v-model:value="searchForm.createdUserId"
            placeholder="请选择创建人"
            allow-clear
            style="width: 150px"
            @change="handleCreatedUserChange"
          >
            <a-select-option
              v-for="user in searchOptions.createdUsers"
              :key="user.id"
              :value="user.id"
            >
              {{ user.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-space>
            <a-button type="primary" @click="handleSearch" :loading="loading">
              <SearchOutlined />
              搜索
            </a-button>
            <a-button @click="handleReset">
              <ReloadOutlined />
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 工价列表区域 -->
    <a-card :bordered="false" class="table-card">
      <template #title>
        <span>工价列表</span>
        <a-tag color="blue" style="margin-left: 8px">
          共 {{ pagination.total }} 条记录
        </a-tag>
      </template>

      <template #extra>
        <a-space>
          <a-button
            type="primary"
            v-permission="'salary_rate:create'"
            @click="showCreateModal"
          >
            <PlusOutlined />
            新增工价
          </a-button>
          <a-button @click="refreshData">
            <ReloadOutlined />
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataList"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'type'">
            <a-tag :color="getWageTypeColor(record.type)">
              {{ getWageTypeLabel(record.type) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'pattern'">
            <span v-if="record.pattern">
              {{ record.pattern.name }}
              <span v-if="record.pattern.code" class="pattern-code">({{ record.pattern.code }})</span>
            </span>
            <span v-else class="text-gray">-</span>
          </template>
          <template v-else-if="column.key === 'patternWage'">
            <span v-if="record.patternWage" class="wage-value">¥{{ record.patternWage }}/万针</span>
            <span v-else class="text-gray">-</span>
          </template>
          <template v-else-if="column.key === 'incentiveWage'">
            <span v-if="record.incentiveWage && record.incentiveWage.length > 0" class="incentive-wage">
              {{ formatIncentiveWageDisplay(record.incentiveWage) }}
            </span>
            <span v-else class="text-gray">无</span>
          </template>
          <template v-else-if="column.key === 'changePieceWage'">
            <span v-if="record.changePieceWage" class="wage-value">¥{{ record.changePieceWage }}/次</span>
            <span v-else class="text-gray">-</span>
          </template>
          <template v-else-if="column.key === 'changeLineWage'">
            <span v-if="record.changeLineWage" class="wage-value">¥{{ record.changeLineWage }}/次</span>
            <span v-else class="text-gray">-</span>
          </template>
          <template v-else-if="column.key === 'createdUser'">
            <span v-if="record.createdUser">{{ record.createdUser.name }}</span>
            <span v-else class="text-gray">-</span>
          </template>
          <template v-else-if="column.key === 'createdAt'">
            {{ formatDateTime(record.createdAt) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                type="link"
                size="small"
                v-permission="'salary_rate:update'"
                @click="showEditModal(record)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                v-permission="'salary_rate:delete'"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalMode === 'create' ? '新增工价配置' : '编辑工价配置'"
      :confirm-loading="modalLoading"
      width="800px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-form-item label="工价类型" name="type">
          <a-radio-group v-model:value="formData.type" @change="handleTypeChange">
            <a-radio
              v-for="typeOption in wageTypeOptions"
              :key="typeOption.value"
              :value="typeOption.value"
            >
              {{ typeOption.label }}
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 花样工价配置 -->
        <template v-if="formData.type === WageType.PATTERN_WAGE">
          <a-form-item label="选择花样" name="patternId">
            <a-select
              v-model:value="formData.patternId"
              placeholder="请选择花样"
              show-search
              :filter-option="filterPatternOption"
            >
              <a-select-option
                v-for="pattern in searchOptions.patterns"
                :key="pattern.id"
                :value="pattern.id"
              >
                {{ pattern.name }}{{ pattern.code ? ` (${pattern.code})` : '' }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="花样工价（元/万针）" name="patternWage">
            <a-input-number
              v-model:value="formData.patternWage"
              :min="0"
              :precision="2"
              placeholder="请输入花样工价"
              style="width: 100%"
            />
          </a-form-item>

          <a-form-item label="激励工价配置">
            <IncentiveWageForm
              v-model="formData.incentiveWage"
              :disabled="false"
              @validate="handleIncentiveWageValidate"
            />
          </a-form-item>
        </template>

        <!-- 换片单价配置 -->
        <template v-else-if="formData.type === WageType.CHANGE_PIECE_WAGE">
          <a-form-item label="换片单价（元/次）" name="changePieceWage">
            <a-input-number
              v-model:value="formData.changePieceWage"
              :min="0"
              :precision="2"
              placeholder="请输入换片单价"
              style="width: 100%"
            />
          </a-form-item>
        </template>

        <!-- 换线单价配置 -->
        <template v-else-if="formData.type === WageType.CHANGE_LINE_WAGE">
          <a-form-item label="换线单价（元/次）" name="changeLineWage">
            <a-input-number
              v-model:value="formData.changeLineWage"
              :min="0"
              :precision="2"
              placeholder="请输入换线单价"
              style="width: 100%"
            />
          </a-form-item>
        </template>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { wageConfigApi } from '../../api/wageConfig'
import type {
  WageConfig,
  IncentiveWageItem,
  CreateWageConfigRequest,
  WageConfigSearchOptions
} from '../../types/wageConfig'
import {
  WageType,
  WageTypeOptions,
  WageTypeLabels,
  formatIncentiveWage,
  validateIncentiveWage,
  createDefaultIncentiveWageItem
} from '../../types/wageConfig'
import { patternApi } from '../../api/pattern'
// import IncentiveWageForm from '../../components/salary/IncentiveWageForm.vue'

// 响应式数据
const loading = ref(false)
const dataList = ref<WageConfig[]>([])
const modalVisible = ref(false)
const modalLoading = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  search: '',
  type: undefined as WageType | undefined,
  patternId: undefined as number | undefined,
  createdUserId: undefined as number | undefined
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 搜索选项
const searchOptions = ref<WageConfigSearchOptions>({
  patterns: [],
  createdUsers: [],
  types: []
})

// 工价类型选项
const wageTypeOptions = WageTypeOptions

// 表格列配置
const columns = [
  {
    title: '工价类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '关联花样',
    dataIndex: 'pattern',
    key: 'pattern',
    width: 150
  },
  {
    title: '花样工价',
    dataIndex: 'patternWage',
    key: 'patternWage',
    width: 120
  },
  {
    title: '激励工价',
    dataIndex: 'incentiveWage',
    key: 'incentiveWage',
    width: 200
  },
  {
    title: '换片单价',
    dataIndex: 'changePieceWage',
    key: 'changePieceWage',
    width: 120
  },
  {
    title: '换线单价',
    dataIndex: 'changeLineWage',
    key: 'changeLineWage',
    width: 120
  },
  {
    title: '创建人',
    dataIndex: 'createdUser',
    key: 'createdUser',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right'
  }
]

// 表单数据
const formData = reactive<CreateWageConfigRequest & { id?: number }>({
  type: WageType.PATTERN_WAGE,
  patternId: undefined,
  patternWage: undefined,
  incentiveWage: [],
  changePieceWage: undefined,
  changeLineWage: undefined
})

// 表单验证规则
const formRules = {
  type: [
    { required: true, message: '请选择工价类型', trigger: 'change' }
  ],
  patternId: [
    {
      required: true,
      message: '请选择花样',
      trigger: 'change',
      validator: (_rule: any, value: any) => {
        if (formData.type === WageType.PATTERN_WAGE && !value) {
          return Promise.reject('花样工价类型必须选择花样')
        }
        return Promise.resolve()
      }
    }
  ],
  patternWage: [
    {
      validator: (_rule: any, value: any) => {
        if (formData.type === WageType.PATTERN_WAGE && !value && (!formData.incentiveWage || formData.incentiveWage.length === 0)) {
          return Promise.reject('花样工价类型必须设置花样工价或激励工价')
        }
        return Promise.resolve()
      },
      trigger: 'blur'
    }
  ],
  changePieceWage: [
    {
      required: true,
      message: '请输入换片单价',
      trigger: 'blur',
      validator: (_rule: any, value: any) => {
        if (formData.type === WageType.CHANGE_PIECE_WAGE && !value) {
          return Promise.reject('换片单价类型必须设置换片单价')
        }
        return Promise.resolve()
      }
    }
  ],
  changeLineWage: [
    {
      required: true,
      message: '请输入换线单价',
      trigger: 'blur',
      validator: (_rule: any, value: any) => {
        if (formData.type === WageType.CHANGE_LINE_WAGE && !value) {
          return Promise.reject('换线单价类型必须设置换线单价')
        }
        return Promise.resolve()
      }
    }
  ]
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const response = await wageConfigApi.getWageConfigList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      search: searchForm.search || undefined,
      type: searchForm.type,
      patternId: searchForm.patternId,
      createdUserId: searchForm.createdUserId
    })

    dataList.value = response.wageConfigs
    pagination.total = response.total
  } catch (error: any) {
    console.error('加载工价配置数据失败:', error)
    // 错误消息由 request.ts 的响应拦截器统一处理，这里不再重复显示

    // 清空数据
    dataList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 加载搜索选项
const loadSearchOptions = async () => {
  try {
    const options = await wageConfigApi.getSearchOptions()
    searchOptions.value = options
  } catch (error) {
    console.error('加载搜索选项失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  searchForm.search = ''
  searchForm.type = undefined
  searchForm.patternId = undefined
  searchForm.createdUserId = undefined
  pagination.current = 1
  loadData()
}

// 花样变化
const handlePatternChange = () => {
  // 花样变化时可以触发搜索或其他逻辑
}

// 创建人变化
const handleCreatedUserChange = () => {
  // 创建人变化时可以触发搜索或其他逻辑
}

// 刷新数据
const refreshData = () => {
  loadData()
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

// 显示创建模态框
const showCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  modalVisible.value = true
}



// 重置表单
const resetForm = () => {
  formData.type = WageType.PATTERN_WAGE
  formData.patternId = undefined
  formData.patternWage = undefined
  formData.incentiveWage = []
  formData.changePieceWage = undefined
  formData.changeLineWage = undefined
  delete formData.id
}

// 模态框确认
const handleModalOk = async () => {
  try {
    await formRef.value.validate()

    // 验证激励工价
    if (formData.incentiveWage && formData.incentiveWage.length > 0) {
      const validation = validateIncentiveWage(formData.incentiveWage)
      if (!validation.valid) {
        message.error(validation.message)
        return
      }
    }

    modalLoading.value = true

    if (modalMode.value === 'create') {
      await wageConfigApi.createWageConfig(formData)
      message.success('创建工价配置成功')
    } else {
      await wageConfigApi.updateWageConfig(formData.id!, formData)
      message.success('更新工价配置成功')
    }

    modalVisible.value = false
    loadData()
  } catch (error: any) {
    console.error('保存工价配置失败:', error)
    // 错误消息由 request.ts 的响应拦截器统一处理，这里不再重复显示
  } finally {
    modalLoading.value = false
  }
}

// 模态框取消
const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 删除
const handleDelete = (record: WageConfig) => {
  const typeName = getWageTypeLabel(record.type)
  const description = record.pattern ? `${typeName} - ${record.pattern.name}` : typeName

  Modal.confirm({
    title: '确认删除',
    content: `确定要删除工价配置"${description}"吗？`,
    onOk: async () => {
      try {
        await wageConfigApi.deleteWageConfig(record.id)
        message.success('删除成功')
        loadData()
      } catch (error: any) {
        console.error('删除工价配置失败:', error)
        // 错误消息由 request.ts 的响应拦截器统一处理，这里不再重复显示
      }
    }
  })
}

// 工具函数
const getWageTypeLabel = (type: WageType): string => {
  return WageTypeLabels[type] || '未知类型'
}

const getWageTypeColor = (type: WageType): string => {
  switch (type) {
    case WageType.PATTERN_WAGE:
      return 'blue'
    case WageType.CHANGE_PIECE_WAGE:
      return 'green'
    case WageType.CHANGE_LINE_WAGE:
      return 'orange'
    default:
      return 'default'
  }
}

const formatIncentiveWageDisplay = (incentiveWage: IncentiveWageItem[]): string => {
  if (!incentiveWage || incentiveWage.length === 0) return '无'
  return `${incentiveWage.length}条规则`
}

const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const filterPatternOption = (input: string, option: any): boolean => {
  const pattern = searchOptions.value.patterns.find(p => p.id === option.value)
  if (!pattern) return false

  const searchText = input.toLowerCase()
  return pattern.name.toLowerCase().includes(searchText) ||
         (pattern.code && pattern.code.toLowerCase().includes(searchText))
}

// 工价类型变化处理
const handleTypeChange = () => {
  // 清空相关字段
  formData.patternId = undefined
  formData.patternWage = undefined
  formData.incentiveWage = []
  formData.changePieceWage = undefined
  formData.changeLineWage = undefined
}

// 激励工价验证处理
const handleIncentiveWageValidate = (valid: boolean, errors: string[]) => {
  if (!valid && errors.length > 0) {
    console.warn('激励工价验证失败:', errors)
  }
}

// 显示编辑模态框
const showEditModal = (record: WageConfig) => {
  modalMode.value = 'edit'
  Object.assign(formData, {
    id: record.id,
    type: record.type,
    patternId: record.patternId,
    patternWage: record.patternWage,
    incentiveWage: record.incentiveWage ? [...record.incentiveWage] : [],
    changePieceWage: record.changePieceWage,
    changeLineWage: record.changeLineWage
  })
  modalVisible.value = true
}

// 组件挂载时加载数据
onMounted(() => {
  loadSearchOptions()
  loadData()
})
</script>

<style scoped>
.salary-rates {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-card :deep(.ant-card-body) {
  padding: 20px;
}

.search-form {
  width: 100%;
}

.search-form :deep(.ant-form-item) {
  margin-bottom: 16px;
  margin-right: 24px;
}

.search-form :deep(.ant-form-item-label) {
  font-weight: 500;
  color: #262626;
}

.search-form :deep(.ant-select),
.search-form :deep(.ant-input) {
  border-radius: 6px;
}

.search-form :deep(.ant-btn) {
  border-radius: 6px;
  font-weight: 500;
}

/* 表格卡片 */
.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-card :deep(.ant-card-body) {
  padding: 0;
}

.table-card :deep(.ant-table) {
  border-radius: 0 0 8px 8px;
}

.table-card :deep(.ant-card-head) {
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 8px 8px 0 0;
}

.table-card :deep(.ant-card-head-title) {
  font-weight: 600;
  color: #262626;
}

.table-card :deep(.ant-card-extra) {
  color: #262626;
}

.wage-value {
  font-weight: 500;
  color: #1890ff;
}

.pattern-code {
  color: #999;
  font-size: 12px;
}

.text-gray {
  color: #999;
}

.text-center {
  text-align: center;
}

.incentive-wage {
  color: #52c41a;
  font-size: 12px;
}

:deep(.ant-table .ant-table-tbody > tr > td) {
  vertical-align: top;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .salary-rates {
    padding: 16px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-card :deep(.ant-form-item) {
    margin-bottom: 16px;
    margin-right: 0;
  }

  .search-card :deep(.ant-input),
  .search-card :deep(.ant-select) {
    width: 100% !important;
  }

  .search-card :deep(.ant-space) {
    width: 100%;
    justify-content: center;
  }
}
</style>
