<!--
  工资列表页面
  {{CHENGQI: Action: Modified; Timestamp: 2025-07-28 12:00:00 +08:00; Reason: 完善工资列表页面, 实现完整CRUD功能; Principle_Applied: 模块化页面设计;}}
-->

<template>
  <div class="salary-list">
    <!-- 搜索卡片 -->
    <a-card class="search-card" :bordered="false">
      <a-form
        :model="searchForm"
        layout="inline"
        class="search-form"
        @finish="handleSearch"
      >
        <a-form-item label="搜索关键词">
          <a-input
            v-model:value="searchForm.search"
            placeholder="设备名称、花样名称、备注"
            allow-clear
            style="width: 200px"
            @press-enter="handleSearch"
          />
        </a-form-item>
        <a-form-item label="员工编码">
          <a-select
            v-model:value="searchForm.userCode"
            placeholder="选择员工"
            allow-clear
            show-search
            :filter-option="filterUserOption"
            style="width: 150px"
          >
            <a-select-option
              v-for="user in searchOptions.users"
              :key="user.userCode"
              :value="user.userCode"
            >
              {{ user.userCode }} - {{ user.realName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="设备">
          <a-select
            v-model:value="searchForm.deviceId"
            placeholder="选择设备"
            allow-clear
            show-search
            :filter-option="filterDeviceOption"
            style="width: 150px"
          >
            <a-select-option
              v-for="device in searchOptions.devices"
              :key="device.id"
              :value="device.id"
            >
              {{ device.name }}{{ device.code ? ` (${device.code})` : '' }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="花样">
          <a-select
            v-model:value="searchForm.patternId"
            placeholder="选择花样"
            allow-clear
            show-search
            :filter-option="filterPatternOption"
            style="width: 150px"
          >
            <a-select-option
              v-for="pattern in searchOptions.patterns"
              :key="pattern.id"
              :value="pattern.id"
            >
              {{ pattern.name }}{{ pattern.code ? ` (${pattern.code})` : '' }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="时间范围">
          <a-range-picker
            v-model:value="dateRange"
            format="YYYY-MM-DD"
            style="width: 240px"
            @change="handleDateRangeChange"
          />
        </a-form-item>
        <a-form-item>
          <a-space>
            <a-button type="primary" html-type="submit" :icon="h(SearchOutlined)">
              搜索
            </a-button>
            <a-button @click="resetSearch">
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 数据卡片 -->
    <a-card title="工资列表" :bordered="false">
      <!-- 操作栏 -->
      <template #extra>
        <a-space>
          <a-button
            type="primary"
            :icon="h(PlusOutlined)"
            v-permission="'salary_list:create'"
            @click="showCreateModal"
          >
            新增工资记录
          </a-button>
          <a-button
            :icon="h(ExportOutlined)"
            v-permission="'salary_list:export'"
            @click="handleExport"
            :loading="exportLoading"
          >
            导出数据
          </a-button>
          <a-button
            :icon="h(ReloadOutlined)"
            @click="refreshData"
            :loading="loading"
          >
            刷新
          </a-button>
        </a-space>
      </template>

      <!-- 批量操作栏 -->
      <div v-if="selectedRowKeys.length > 0" class="batch-actions">
        <a-alert
          :message="`已选择 ${selectedRowKeys.length} 项`"
          type="info"
          show-icon
          style="margin-bottom: 16px"
        >
          <template #action>
            <a-space>
              <a-button
                size="small"
                @click="clearSelection"
              >
                取消选择
              </a-button>
              <a-button
                size="small"
                type="primary"
                danger
                v-permission="'salary_list:delete'"
                @click="handleBatchDelete"
                :loading="batchDeleteLoading"
              >
                批量删除
              </a-button>
            </a-space>
          </template>
        </a-alert>
      </div>

      <!-- 数据表格 -->
      <a-table
        :columns="columns"
        :data-source="dataList"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'userCode'">
            <span>{{ record.userCode || '-' }}</span>
          </template>
          <template v-else-if="column.key === 'device'">
            <span>{{ record.device?.name || '-' }}</span>
            <div v-if="record.device?.code" class="text-gray-500 text-xs">
              {{ record.device.code }}
            </div>
          </template>
          <template v-else-if="column.key === 'pattern'">
            <span>{{ record.pattern?.name || '-' }}</span>
            <div v-if="record.pattern?.code" class="text-gray-500 text-xs">
              {{ record.pattern.code }}
            </div>
          </template>
          <template v-else-if="column.key === 'workDuration'">
            <span>{{ formatWorkDuration(record.loginTime, record.logoutTime) }}</span>
          </template>
          <template v-else-if="column.key === 'patternStitch'">
            <span>{{ formatStitches(record.patternStitch) }}</span>
          </template>
          <template v-else-if="column.key === 'totalWage'">
            <span class="font-semibold text-green-600">
              {{ formatWageAmount(record.totalWage) }}
            </span>
          </template>
          <template v-else-if="column.key === 'productiveWage'">
            <span>{{ formatWageAmount(record.productiveWage) }}</span>
          </template>
          <template v-else-if="column.key === 'subsidyAmount'">
            <span class="text-blue-600">{{ formatWageAmount(record.subsidyAmount) }}</span>
          </template>
          <template v-else-if="column.key === 'fineAmount'">
            <span class="text-red-600">{{ formatWageAmount(record.fineAmount) }}</span>
          </template>
          <template v-else-if="column.key === 'createdAt'">
            <span>{{ formatDateTime(record.createdAt) }}</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button
                type="link"
                size="small"
                @click="showDetailModal(record)"
              >
                查看
              </a-button>
              <a-button
                type="link"
                size="small"
                v-permission="'salary_list:update'"
                @click="showEditModal(record)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                size="small"
                danger
                v-permission="'salary_list:delete'"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 创建/编辑模态框 -->
    <a-modal
      v-model:open="modalVisible"
      :title="modalMode === 'create' ? '新增工资记录' : '编辑工资记录'"
      :confirm-loading="modalLoading"
      width="800px"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="员工姓名" name="employeeName">
              <a-input v-model:value="formData.employeeName" placeholder="请输入员工姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="工资月份" name="salaryMonth">
              <a-month-picker 
                v-model:value="formData.salaryMonth" 
                placeholder="请选择工资月份"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="基本工资" name="baseSalary">
              <a-input-number
                v-model:value="formData.baseSalary"
                :min="0"
                :precision="2"
                placeholder="基本工资"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="绩效工资" name="performanceSalary">
              <a-input-number
                v-model:value="formData.performanceSalary"
                :min="0"
                :precision="2"
                placeholder="绩效工资"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="加班费" name="overtimePay">
              <a-input-number
                v-model:value="formData.overtimePay"
                :min="0"
                :precision="2"
                placeholder="加班费"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="津贴补助" name="allowance">
              <a-input-number
                v-model:value="formData.allowance"
                :min="0"
                :precision="2"
                placeholder="津贴补助"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="扣除项" name="deduction">
              <a-input-number
                v-model:value="formData.deduction"
                :min="0"
                :precision="2"
                placeholder="扣除项"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="实发工资" name="totalAmount">
              <a-input-number
                v-model:value="calculatedTotal"
                :precision="2"
                placeholder="自动计算"
                disabled
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 审核模态框 -->
    <a-modal
      v-model:open="approveModalVisible"
      title="审核工资记录"
      :confirm-loading="approveLoading"
      @ok="handleApproveOk"
      @cancel="approveModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="审核结果">
          <a-radio-group v-model:value="approveForm.result">
            <a-radio value="approved">通过</a-radio>
            <a-radio value="rejected">拒绝</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="审核意见">
          <a-textarea 
            v-model:value="approveForm.comment" 
            placeholder="请输入审核意见"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h, onMounted, computed, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  SearchOutlined,
  ExportOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'
import { wageApi } from '../../api/wage'
import type {
  Wage,
  WageListQuery,
  WageSearchOptions,
  WageFormData,
  CreateWageRequest,
  UpdateWageRequest
} from '../../types/wage'
import {
  formatWageAmount,
  formatStitches,
  formatWorkDuration,
  validateWageForm,
  createDefaultWageFormData
} from '../../types/wage'
import dayjs from 'dayjs'

// 响应式数据
const loading = ref(false)
const exportLoading = ref(false)
const batchDeleteLoading = ref(false)
const dataList = ref<Wage[]>([])
const modalVisible = ref(false)
const modalLoading = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const formRef = ref()
const selectedRowKeys = ref<number[]>([])
const dateRange = ref<[dayjs.Dayjs, dayjs.Dayjs] | undefined>()

// 搜索表单
const searchForm = reactive<WageListQuery>({
  search: '',
  userCode: undefined,
  deviceId: undefined,
  patternId: undefined,
  startDate: undefined,
  endDate: undefined
})

// 搜索选项
const searchOptions = ref<WageSearchOptions>({
  devices: [],
  patterns: [],
  users: []
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: '员工编码',
    dataIndex: 'userCode',
    key: 'userCode',
    width: 100
  },
  {
    title: '设备',
    dataIndex: 'device',
    key: 'device',
    width: 120
  },
  {
    title: '花样',
    dataIndex: 'pattern',
    key: 'pattern',
    width: 120
  },
  {
    title: '工作时长',
    key: 'workDuration',
    width: 100
  },
  {
    title: '花样针数',
    dataIndex: 'patternStitch',
    key: 'patternStitch',
    width: 100
  },
  {
    title: '产量工资',
    dataIndex: 'productiveWage',
    key: 'productiveWage',
    width: 100
  },
  {
    title: '奖励金额',
    dataIndex: 'subsidyAmount',
    key: 'subsidyAmount',
    width: 100
  },
  {
    title: '罚款金额',
    dataIndex: 'fineAmount',
    key: 'fineAmount',
    width: 100
  },
  {
    title: '总工资',
    dataIndex: 'totalWage',
    key: 'totalWage',
    width: 120,
    fixed: 'right'
  },
  {
    title: '创建时间',
    dataIndex: 'createdAt',
    key: 'createdAt',
    width: 160
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right'
  }
]

// 表单数据
const formData = reactive<WageFormData>(createDefaultWageFormData())

// 行选择配置
const rowSelection = computed(() => ({
  selectedRowKeys: selectedRowKeys.value,
  onChange: (keys: number[]) => {
    selectedRowKeys.value = keys
  },
  getCheckboxProps: (record: Wage) => ({
    disabled: false,
    name: record.id.toString(),
  }),
}))

// 审核表单
const approveForm = reactive({
  result: 'approved',
  comment: ''
})

// 表单验证规则
const formRules = {
  userCode: [
    { type: 'number', min: 1, message: '员工编码必须大于0', trigger: 'blur' }
  ]
}

// 格式化日期时间
const formatDateTime = (dateTime?: string) => {
  if (!dateTime) return '-'
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 筛选选项方法
const filterUserOption = (input: string, option: any) => {
  const user = searchOptions.value.users.find(u => u.userCode === option.value)
  if (!user) return false
  return user.realName.toLowerCase().includes(input.toLowerCase()) ||
         user.userCode.toString().includes(input)
}

const filterDeviceOption = (input: string, option: any) => {
  const device = searchOptions.value.devices.find(d => d.id === option.value)
  if (!device) return false
  return device.name.toLowerCase().includes(input.toLowerCase()) ||
         (device.code && device.code.toLowerCase().includes(input.toLowerCase()))
}

const filterPatternOption = (input: string, option: any) => {
  const pattern = searchOptions.value.patterns.find(p => p.id === option.value)
  if (!pattern) return false
  return pattern.name.toLowerCase().includes(input.toLowerCase()) ||
         (pattern.code && pattern.code.toLowerCase().includes(input.toLowerCase()))
}

// 日期范围变化处理
const handleDateRangeChange = (dates: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
  if (dates) {
    searchForm.startDate = dates[0].format('YYYY-MM-DD')
    searchForm.endDate = dates[1].format('YYYY-MM-DD')
  } else {
    searchForm.startDate = undefined
    searchForm.endDate = undefined
  }
}

// 清除选择
const clearSelection = () => {
  selectedRowKeys.value = []
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const response = await wageApi.getWageList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    })

    dataList.value = response.wages
    pagination.total = response.total

  } catch (error) {
    message.error('加载数据失败')
    console.error('加载工资列表数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载搜索选项
const loadSearchOptions = async () => {
  try {
    const options = await wageApi.getSearchOptions()
    searchOptions.value = options
  } catch (error) {
    console.error('加载搜索选项失败:', error)
  }
}

// 搜索方法
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置搜索
const resetSearch = () => {
  searchForm.search = ''
  searchForm.userCode = undefined
  searchForm.deviceId = undefined
  searchForm.patternId = undefined
  searchForm.startDate = undefined
  searchForm.endDate = undefined
  dateRange.value = undefined
  pagination.current = 1
  loadData()
}

const refreshData = () => {
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const showCreateModal = () => {
  modalMode.value = 'create'
  resetForm()
  modalVisible.value = true
}

const showEditModal = (record: any) => {
  modalMode.value = 'edit'
  Object.assign(formData, record)
  modalVisible.value = true
}

const showDetailModal = (record: any) => {
  // TODO: 实现详情查看
  message.info('查看详情功能开发中')
}

const resetForm = () => {
  Object.assign(formData, createDefaultWageFormData())
}

const handleModalOk = async () => {
  try {
    await formRef.value.validate()

    // 验证表单数据
    const validation = validateWageForm(formData)
    if (!validation.valid) {
      message.error(validation.errors[0])
      return
    }

    modalLoading.value = true

    if (modalMode.value === 'create') {
      const createData: CreateWageRequest = {
        deviceId: formData.deviceId,
        userCode: formData.userCode,
        loginTime: formData.loginTime,
        logoutTime: formData.logoutTime,
        patternId: formData.patternId,
        patternStitch: formData.patternStitch,
        patternWage: formData.patternWage,
        productiveWage: formData.productiveWage,
        changePieceCount: formData.changePieceCount,
        changePieceWage: formData.changePieceWage,
        changeLineCount: formData.changeLineCount,
        changeLineWage: formData.changeLineWage,
        fineAmount: formData.fineAmount,
        subsidyAmount: formData.subsidyAmount,
        totalWage: formData.totalWage,
        latheCount: formData.latheCount,
        remark: formData.remark
      }
      await wageApi.createWage(createData)
      message.success('创建工资记录成功')
    } else {
      const updateData: UpdateWageRequest = {
        deviceId: formData.deviceId,
        userCode: formData.userCode,
        loginTime: formData.loginTime,
        logoutTime: formData.logoutTime,
        patternId: formData.patternId,
        patternStitch: formData.patternStitch,
        patternWage: formData.patternWage,
        productiveWage: formData.productiveWage,
        changePieceCount: formData.changePieceCount,
        changePieceWage: formData.changePieceWage,
        changeLineCount: formData.changeLineCount,
        changeLineWage: formData.changeLineWage,
        fineAmount: formData.fineAmount,
        subsidyAmount: formData.subsidyAmount,
        totalWage: formData.totalWage,
        latheCount: formData.latheCount,
        remark: formData.remark
      }
      await wageApi.updateWage(formData.id!, updateData)
      message.success('更新工资记录成功')
    }

    modalVisible.value = false
    loadData()
  } catch (error) {
    message.error('保存失败')
    console.error('保存失败:', error)
  } finally {
    modalLoading.value = false
  }
}

const handleModalCancel = () => {
  modalVisible.value = false
  resetForm()
}

// 导出数据
const handleExport = async () => {
  try {
    exportLoading.value = true
    const blob = await wageApi.exportWages({
      ...searchForm
    })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `工资记录_${dayjs().format('YYYY-MM-DD')}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    message.success('导出成功')
  } catch (error) {
    message.error('导出失败')
    console.error('导出失败:', error)
  } finally {
    exportLoading.value = false
  }
}

const handleDelete = (record: Wage) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除这条工资记录吗？`,
    onOk: async () => {
      try {
        await wageApi.deleteWage(record.id)
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
        console.error('删除失败:', error)
      }
    }
  })
}

const handleBatchDelete = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的记录')
    return
  }

  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 条工资记录吗？`,
    onOk: async () => {
      try {
        batchDeleteLoading.value = true
        await wageApi.batchDeleteWages(selectedRowKeys.value)
        message.success('批量删除成功')
        clearSelection()
        loadData()
      } catch (error) {
        message.error('批量删除失败')
        console.error('批量删除失败:', error)
      } finally {
        batchDeleteLoading.value = false
      }
    }
  })
}

// 监听表单数据变化，自动计算工资
watch([
  () => formData.patternStitch,
  () => formData.patternWage,
  () => formData.changePieceCount,
  () => formData.changePieceWage,
  () => formData.changeLineCount,
  () => formData.changeLineWage,
  () => formData.fineAmount,
  () => formData.subsidyAmount
], () => {
  // 计算产量工资
  if (formData.patternStitch && formData.patternWage) {
    formData.productiveWage = (formData.patternStitch * formData.patternWage) / 10000
  }

  // 计算总工资
  const productiveWage = formData.productiveWage || 0
  const subsidyAmount = formData.subsidyAmount || 0
  const changePieceWage = formData.changePieceWage || 0
  const changeLineWage = formData.changeLineWage || 0
  const fineAmount = formData.fineAmount || 0

  formData.totalWage = productiveWage + subsidyAmount + changePieceWage + changeLineWage - fineAmount
})

// 组件挂载时加载数据
onMounted(() => {
  loadSearchOptions()
  loadData()
})
</script>

<style scoped>
.salary-list {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索卡片 */
.search-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-card :deep(.ant-card-body) {
  padding: 20px;
}

.search-form {
  width: 100%;
}

.search-form :deep(.ant-form-item) {
  margin-bottom: 16px;
}

.search-form :deep(.ant-form-item-label) {
  font-weight: 500;
}

/* 批量操作 */
.batch-actions {
  margin-bottom: 16px;
}

/* 表格样式 */
.ant-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 金额显示 */
.font-semibold {
  font-weight: 600;
}

.text-green-600 {
  color: #16a34a;
}

.text-blue-600 {
  color: #2563eb;
}

.text-red-600 {
  color: #dc2626;
}

.text-gray-500 {
  color: #6b7280;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

/* 模态框表单 */
.modal-form {
  max-height: 60vh;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .salary-list {
    padding: 16px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form :deep(.ant-form-item) {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>
