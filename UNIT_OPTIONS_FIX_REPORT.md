# 订单表单计量单位选项修复报告

## 问题描述
在新增订单时，计量单位的选择选项是写死的，应该从数据库中获取未禁用的计量单位（em_tag表中type=4且status=1的记录）。

## 问题分析

### 原有实现
在`OrderFormModal.vue`中，计量单位选项是硬编码的：
```typescript
// TODO: 获取记量单位选项（需要后端支持）
unitOptions.value = [
  { label: '件', value: 1 },
  { label: '套', value: 2 },
  { label: '个', value: 3 },
  { label: '米', value: 4 },
  { label: '码', value: 5 }
];
```

### 问题影响
1. **数据不一致**：表单选项与数据库中的计量单位不同步
2. **管理困难**：无法通过计量单位管理功能动态调整选项
3. **用户体验差**：用户在计量单位管理中添加的新单位无法在订单中使用

## 修复方案

### 1. API调用实现
修改`getOptions`方法，从数据库获取计量单位：
```typescript
// 获取计量单位选项
try {
  const unitResponse = await tagApi.getTagList({
    type: TagType.PATTERN_UNIT,  // 4
    status: TagStatus.ENABLED,   // 1
    page: 1,
    pageSize: 100 // 获取所有启用的计量单位
  });
  
  unitOptions.value = unitResponse.tags.map(item => ({
    label: item.name,
    value: item.id
  }));
} catch (error) {
  console.error('获取计量单位选项失败:', error);
  // 如果API调用失败，使用默认选项
  unitOptions.value = [
    { label: '件', value: 1 },
    { label: '套', value: 2 },
    { label: '个', value: 3 },
    { label: '米', value: 4 },
    { label: '码', value: 5 }
  ];
}
```

### 2. 导入依赖
添加必要的导入：
```typescript
import { tagApi } from '@/api/tag';
import { TagType, TagStatus } from '@/types/tag';
```

### 3. 错误处理
- **主要方案**：从数据库获取启用的计量单位
- **备选方案**：API调用失败时使用默认选项
- **用户反馈**：在控制台记录错误信息

## 技术实现

### 后端支持
后端已经支持按类型和状态筛选标签：
```typescript
// TagService.getTagList方法支持的查询参数
interface TagListQuery {
  page?: number;
  pageSize?: number;
  type?: TagType;      // 支持按类型筛选
  status?: TagStatus;  // 支持按状态筛选
  search?: string;
  pid?: number;
}
```

### 前端API
前端tagApi已正确实现：
```typescript
export const tagApi = {
  getTagList: (params: TagListQuery): Promise<TagListResponse> => {
    return http.get('/tags', { params });
  }
};
```

### 数据流程
```
OrderFormModal组件加载
↓
调用getOptions()方法
↓
调用tagApi.getTagList()
↓
传入参数: type=4, status=1
↓
后端查询em_tag表
↓
返回启用的计量单位列表
↓
转换为选项格式
↓
更新unitOptions.value
↓
表单显示动态选项
```

## 修复的文件

### 1. frontend/src/components/order/OrderFormModal.vue
1. **添加导入**：
   ```typescript
   import { tagApi } from '@/api/tag';
   import { TagType, TagStatus } from '@/types/tag';
   ```

2. **修改getOptions方法**：
   - 替换硬编码的计量单位选项
   - 添加API调用获取数据库中的计量单位
   - 添加错误处理和备选方案

### 2. frontend/src/components/order/OrderDetailModal.vue
1. **添加导入**：
   ```typescript
   import { tagApi } from '@/api/tag';
   import { TagType, TagStatus } from '@/types/tag';
   ```

2. **修改getUnitLabel方法**：
   ```typescript
   // 修改前
   const getUnitLabel = (unitId?: number): string => {
     const unitMap: Record<number, string> = {
       1: '件', 2: '套', 3: '个', 4: '米', 5: '码'
     };
     return unitMap[unitId || 1] || '-';
   };

   // 修改后
   const unitOptions = ref<Array<{ label: string; value: number }>>([]);

   const getUnitLabel = (unitId?: number): string => {
     if (!unitId) return '-';
     const unit = unitOptions.value.find(item => item.value === unitId);
     return unit?.label || '-';
   };
   ```

3. **添加getUnitOptions方法**：
   - 从数据库获取计量单位选项
   - 添加错误处理和默认选项

4. **添加生命周期调用**：
   ```typescript
   onMounted(() => {
     getUnitOptions(); // 获取计量单位选项
   });
   ```

## 功能验证

### 1. 基础功能测试
- [ ] 打开新增订单表单
- [ ] 检查计量单位下拉选项是否从数据库加载
- [ ] 验证只显示启用状态的计量单位

### 2. 数据同步测试
- [ ] 在计量单位管理中添加新的计量单位
- [ ] 刷新订单表单，验证新单位出现在选项中
- [ ] 禁用某个计量单位，验证其从选项中消失

### 3. 错误处理测试
- [ ] 模拟API调用失败
- [ ] 验证是否显示默认选项
- [ ] 检查控制台是否有错误日志

### 4. 性能测试
- [ ] 验证API调用是否有防重复机制
- [ ] 检查加载时间是否合理

## 数据库要求

### 计量单位数据示例
确保em_tag表中有type=4的计量单位数据：
```sql
INSERT INTO em_tag (enterprise_id, name, type, status, level, path, created_at, updated_at) VALUES
(1, '件', 4, 1, 1, '1', NOW(), NOW()),
(1, '套', 4, 1, 1, '2', NOW(), NOW()),
(1, '个', 4, 1, 1, '3', NOW(), NOW()),
(1, '米', 4, 1, 1, '4', NOW(), NOW()),
(1, '码', 4, 1, 1, '5', NOW(), NOW());
```

### 字段说明
- `type = 4`：表示计量单位类型
- `status = 1`：表示启用状态
- `level = 1`：计量单位只有一级
- `path`：路径字段，用于排序

## 用户体验改进

### 1. 数据一致性
- 表单选项与数据库实时同步
- 管理员可以通过计量单位管理功能控制选项

### 2. 灵活性
- 支持动态添加新的计量单位
- 支持启用/禁用计量单位

### 3. 可维护性
- 统一的数据来源
- 减少硬编码，提高可维护性

## 后续优化建议

### 1. 缓存机制
考虑添加客户端缓存，避免重复请求：
```typescript
const UNIT_CACHE_KEY = 'order_unit_options';
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟

const getCachedUnitOptions = () => {
  const cached = localStorage.getItem(UNIT_CACHE_KEY);
  if (cached) {
    const { data, timestamp } = JSON.parse(cached);
    if (Date.now() - timestamp < CACHE_DURATION) {
      return data;
    }
  }
  return null;
};
```

### 2. 实时更新
考虑添加WebSocket或轮询机制，实时更新选项：
```typescript
// 监听计量单位管理的变更事件
eventBus.on('unitOptionsChanged', () => {
  getOptions(); // 重新获取选项
});
```

### 3. 默认值优化
根据历史使用频率设置默认选择的计量单位：
```typescript
// 获取最常用的计量单位作为默认值
const getMostUsedUnit = async () => {
  // 查询订单中使用最频繁的计量单位
};
```

## 总结

这次修复解决了以下问题：
1. ✅ 消除了硬编码的计量单位选项
2. ✅ 实现了与数据库的实时同步
3. ✅ 支持通过管理界面动态调整选项
4. ✅ 提供了错误处理和备选方案
5. ✅ 提高了系统的一致性和可维护性

修复后，订单表单中的计量单位选项将从数据库中动态获取，与计量单位管理功能完全同步，为用户提供更好的使用体验。
